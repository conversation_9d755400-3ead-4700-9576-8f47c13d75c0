

Option Explicit

' =============================================================================
' 希尔顿酒店管理系统 - 工作簿事件处理
' 集成登录系统、数据同步和版本检查功能
' =============================================================================

' 应用程序级别事件监听器
Private WithEvents App As Application

Private Sub Workbook_Open()
    On Error GoTo ErrorHandler

    ' 初始化应用程序级别事件监听
    Set App = Application

    ' 安全检查：确保除主页外的所有工作表都被隐藏
    Call HideAllSheetsExceptHomePage

    ' 显示登录表单
    Call ShowLoginForm

    Exit Sub

ErrorHandler:
    ' 发生错误时启动紧急安全模式
    On Error Resume Next
    ' 待实现

    MsgBox "系统启动时发生错误: " & Err.Description & vbCrLf & _
           "已启动安全模式，请联系系统管理员。", vbCritical, "系统错误"

End Sub

Private Sub Workbook_BeforeSave(ByVal SaveAsUI As Boolean, Cancel As Boolean)
    On Error GoTo ErrorHandler


    Exit Sub

ErrorHandler:
    MsgBox "保存过程中发生错误: " & Err.Description, vbCritical, "保存错误"
End Sub

Private Sub Workbook_BeforeClose(Cancel As Boolean)
    On Error GoTo ErrorHandler


    ' 安全退出：隐藏除"主页"外的所有工作表
    Application.StatusBar = "正在保护数据安全..."
    Call HideAllSheetsExceptHomePage
    Application.StatusBar = False

    ' 清除用户Token
    Call ClearUserToken

    ' 清理应用程序事件监听
    Set App = Nothing

    Exit Sub

ErrorHandler:
    ' 即使发生错误也要执行安全措施
    On Error Resume Next
    Call HideAllSheetsExceptHomePage
    Call ClearUserToken
    Set App = Nothing
    Application.StatusBar = False
End Sub

Private Sub Workbook_SheetActivate(ByVal Sh As Object)
    On Error Resume Next

    ' 登录成功后无需验证，用户可以自由访问工作表

    ' 检查是否为业务背景工作表，如果是则确保Add More文字存在并应用统一格式
    If Sh.Name = "2. 业务背景" Then
        ' 检查Add More文字是否存在，如果不存在则重新设置
        Call EnsureAddMoreTextExists(Sh)

        ' 自动应用统一区域格式设置
        Call ApplyRegionFormattingOnActivate(Sh)
    End If
End Sub

' =============================================================================
' 登录相关函数
' =============================================================================

Private Sub ShowLoginForm()
    On Error GoTo ErrorHandler

    ' 检查数据库连接
    If Not TestConnection() Then
        Dim response As VbMsgBoxResult
        response = MsgBox("无法连接到数据库。" & vbCrLf & _
                         "请检查网络连接和数据库服务。" & vbCrLf & vbCrLf & _
                         "是否要以离线模式继续？", _
                         vbYesNo + vbExclamation, "数据库连接失败")

        If response = vbNo Then
            Application.DisplayAlerts = False
            ThisWorkbook.Close SaveChanges:=False
            Application.DisplayAlerts = True
            Exit Sub
        Else
            ' 离线模式 - 显示所有工作表但禁用数据同步
            Call ShowAllSheets
            MsgBox "系统运行在离线模式，数据同步功能已禁用。", vbInformation, "离线模式"
            Exit Sub
        End If
    End If

    ' 显示登录表单
    LoginForm.Show vbModal

    ' 检查登录是否成功（检查是否有用户信息）
    If GetCurrentUser() = "" Then
        ' 用户取消登录或登录失败，关闭工作簿
        Application.DisplayAlerts = False
        ThisWorkbook.Close SaveChanges:=False
        Application.DisplayAlerts = True
    Else
        ' 登录成功后初始化业务背景工作表的动态区域
        Call InitializeBusinessForecastOnLogin
    End If

    Exit Sub

ErrorHandler:
    MsgBox "显示登录表单时发生错误: " & Err.Description, vbCritical, "系统错误"
    Application.DisplayAlerts = False
    ThisWorkbook.Close SaveChanges:=False
    Application.DisplayAlerts = True
End Sub

' 登录成功后初始化业务背景工作表
Private Sub InitializeBusinessForecastOnLogin()
    On Error Resume Next

    ' 检查业务背景工作表是否存在且可见
    Dim ws As Worksheet
    Set ws = Nothing

    ' 尝试不同的工作表名称
    Set ws = ThisWorkbook.Worksheets("2. 业务背景")
    If ws Is Nothing Then Set ws = ThisWorkbook.Worksheets("2.业务背景")

    If Not ws Is Nothing And ws.Visible = xlSheetVisible Then
        ' 设置系统初始化状态，防止在初始化期间触发SegmentForm
        Call SegmentFormManager.SetSystemInitializingState(True)

        ' 登录成功后完整初始化业务背景工作表（包括数据恢复和Add More）
        Call BusinessForecastManager.InitializeUnifiedDynamicRegionOnActivate
        Call BusinessForecastManager.SetupAllAddMoreCells

        ' 初始化完成，恢复正常状态
        Call SegmentFormManager.SetSystemInitializingState(False)

        Call LogManager.LogEvent("INFO", "登录成功后已完成业务背景工作表的完整初始化", "ThisWorkbook", "登录初始化")
    End If
End Sub

' 确保Add More文字存在（工作表激活时检查）
Private Sub EnsureAddMoreTextExists(ws As Worksheet)
    On Error Resume Next

    ' 检查年度重大事件和生意板块管理区域的Add More文字是否存在
    Dim eventsRow As Integer, segmentRow As Integer
    Dim needsSetup As Boolean

    ' 简单检查：扫描E列查找Add More文字
    Dim checkRow As Integer
    Dim addMoreCount As Integer
    addMoreCount = 0

    For checkRow = 41 To 100  ' 扫描可能的范围
        Dim cellValue As String
        cellValue = Trim(CStr(ws.Cells(checkRow, 5).Value))  ' E列

        If InStr(LCase(cellValue), "add more") > 0 Then
            addMoreCount = addMoreCount + 1
        End If
    Next checkRow

    ' 如果Add More文字少于2个（年度重大事件 + 生意板块管理），则重新设置
    If addMoreCount < 2 Then
        Call BusinessForecastManager.SetupAllAddMoreCells
        Call LogManager.LogEvent("INFO", "工作表激活时重新设置Add More文字", ws.Name, "Add More检查")
    End If
End Sub

' 工作表激活时应用统一区域格式设置
Private Sub ApplyRegionFormattingOnActivate(ws As Worksheet)
    On Error Resume Next

    ' 设置系统初始化状态，防止在格式设置期间触发SegmentForm
    Call SegmentFormManager.SetSystemInitializingState(True)

    ' 保存事件状态并禁用事件，防止格式设置时触发其他事件
    Dim originalEventsState As Boolean
    originalEventsState = Application.EnableEvents
    Application.EnableEvents = False

    ' 调用BusinessForecastManager的统一格式设置方法
    Call BusinessForecastManager.ApplyAllRegionStandardFormatting

    ' 恢复事件状态
    Application.EnableEvents = originalEventsState

    ' 清除系统初始化状态
    Call SegmentFormManager.SetSystemInitializingState(False)

    Call LogManager.LogEvent("INFO", "工作表激活时自动应用统一区域格式", ws.Name, "自动格式设置")
End Sub

' 测试工作表激活时的自动格式设置功能
Public Sub TestSheetActivateFormatting()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet("2. 业务背景")

    If ws Is Nothing Then
        MsgBox "找不到'2. 业务背景'工作表", vbCritical, "测试失败"
        Exit Sub
    End If

    Dim testResults As String
    testResults = "=== 工作表激活自动格式设置测试 ===" & vbCrLf & vbCrLf

    ' 模拟工作表激活事件
    testResults = testResults & "正在模拟工作表激活事件..." & vbCrLf
    Call ApplyRegionFormattingOnActivate(ws)

    testResults = testResults & "✓ 工作表激活事件处理完成" & vbCrLf
    testResults = testResults & "✓ 统一区域格式已自动应用" & vbCrLf
    testResults = testResults & "✓ Add More文字检查完成" & vbCrLf & vbCrLf

    testResults = testResults & "格式设置包括:" & vbCrLf
    testResults = testResults & "  • 固定数据区域 (C6:N8, C12:C24, O13:O17, C29:C38)" & vbCrLf
    testResults = testResults & "  • 动态区域 (价格管理、年度重大事件、生意板块管理)" & vbCrLf
    testResults = testResults & "  • 白色背景、微软雅黑、10号字体" & vbCrLf
    testResults = testResults & "  • A6A6A6颜色边框" & vbCrLf & vbCrLf

    testResults = testResults & "=== 测试完成 ==="

    MsgBox testResults, vbInformation, "工作表激活格式设置测试结果"
    Exit Sub

ErrorHandler:
    MsgBox "测试过程中发生错误: " & Err.Description, vbCritical, "测试错误"
End Sub


' =============================================================================
' 错误处理和恢复
' =============================================================================



' 保留原有的工作表变化事件处理（向后兼容）
' 注意：此事件处理已被应用程序级别的事件处理器替代，避免重复处理
' Private Sub Workbook_SheetChange(ByVal Sh As Object, ByVal Target As Range)
'     On Error GoTo ErrorHandler
'
'     ' 检查是否在业务背景工作表
'     If Sh.Name = "2. 业务背景" Then
'         ' 处理版本选择变化
'         If Target.Address = "$O$4" Then
'             Dim newVersion As String
'             newVersion = Trim(CStr(Target.Value))
'
'             If newVersion <> "" Then
'                 Call BusinessForecastManager.HandleVersionChange(Sh, newVersion)
'             End If
'
'         ' 处理酒店生意细分占比区域变化 (C12:C24)
'         ElseIf Target.Column = 3 And Target.Row >= 12 And Target.Row <= 24 Then
'             Call BusinessForecastManager.HandleSegmentRatioChange(Sh, Target)
'
'         ' 处理酒店渠道占比区域变化 (O13:O17)
'         ElseIf Target.Column = 15 And Target.Row >= 13 And Target.Row <= 17 Then
'             Call BusinessForecastManager.HandleChannelRatioChange(Sh, Target)
'
'         ' 处理Hotel GOB Analysis区域变化 (C29:C38)
'         ElseIf Target.Column = 3 And Target.Row >= 29 And Target.Row <= 38 Then
'             Call BusinessForecastManager.HandleHotelGOBChange(Sh, Target)
'         End If
'     End If
'
'     Exit Sub
'
' ErrorHandler:
'     ' 记录错误但不中断用户操作
'     On Error Resume Next
'     Call LogManager.LogEvent("ERROR", "工作表变化事件处理异常: " & Err.Description, Sh.Name, "事件处理")
' End Sub

' 处理工作表双击事件
Private Sub Workbook_SheetBeforeDoubleClick(ByVal Sh As Object, ByVal Target As Range, Cancel As Boolean)
    On Error GoTo ErrorHandler

    ' 检查是否在酒店信息工作表
    If Sh.Name = "1.酒店信息" Then
        ' 取消默认的双击行为（如进入编辑模式）
        Cancel = True

        ' 调用酒店信息管理模块的双击处理函数
        Call HotelInfoManager.HandleHotelInfoDoubleClick(Target)
    End If

    Exit Sub

ErrorHandler:
    ' 记录错误但不中断用户操作
    On Error Resume Next
    ' 可以在这里添加错误日志记录
End Sub

' 应用程序错误处理
Private Sub Workbook_WindowActivate(ByVal Wn As Window)
    On Error Resume Next

    ' 显示用户信息（登录成功后直接显示）
    Application.StatusBar = "用户: " & GetCurrentUser() & " | 酒店: " & GetCurrentHotelName()
End Sub

Private Sub Workbook_WindowDeactivate(ByVal Wn As Window)
    On Error Resume Next

    ' 清除状态栏
    Application.StatusBar = False
End Sub


' =============================================================================
' Add More功能测试和验证
' =============================================================================

' 测试所有Add More功能是否正常工作
Public Sub TestAllAddMoreFunctions()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet("2. 业务背景")

    If ws Is Nothing Then
        MsgBox "找不到业务背景工作表", vbCritical, "测试失败"
        Exit Sub
    End If

    Dim testResults As String
    testResults = "=== Add More功能测试报告 ===" & vbCrLf & vbCrLf

    ' 1. 测试Add More文字是否存在
    Dim addMoreCount As Integer
    addMoreCount = 0
    Dim checkRow As Integer

    For checkRow = 41 To 100
        Dim cellValue As String
        cellValue = Trim(CStr(ws.Cells(checkRow, 5).Value))

        If InStr(LCase(cellValue), "add more") > 0 Then
            addMoreCount = addMoreCount + 1
            testResults = testResults & "✓ 找到Add More文字: E" & checkRow & " = " & cellValue & vbCrLf
        End If
    Next checkRow

    testResults = testResults & vbCrLf & "Add More文字总数: " & addMoreCount & vbCrLf

    ' 2. 测试年度重大事件Add More功能
    testResults = testResults & vbCrLf & "年度重大事件Add More: "
    If TestEventsAddMore() Then
        testResults = testResults & "✓ 正常" & vbCrLf
    Else
        testResults = testResults & "✗ 异常" & vbCrLf
    End If

    ' 3. 测试生意板块管理窗体功能
    testResults = testResults & "生意板块管理窗体: "
    If TestSegmentForm() Then
        testResults = testResults & "✓ 正常" & vbCrLf
    Else
        testResults = testResults & "✗ 异常" & vbCrLf
    End If

    testResults = testResults & vbCrLf & "=== 测试完成 ==="

    MsgBox testResults, vbInformation, "Add More功能测试"
    Exit Sub

ErrorHandler:
    MsgBox "测试过程中发生错误: " & Err.Description, vbCritical, "测试错误"
End Sub

' 测试年度重大事件Add More功能
Private Function TestEventsAddMore() As Boolean
    On Error GoTo ErrorHandler

    TestEventsAddMore = True
    ' 这里可以添加具体的测试逻辑
    Exit Function

ErrorHandler:
    TestEventsAddMore = False
End Function

' 测试生意板块管理窗体功能
Private Function TestSegmentForm() As Boolean
    On Error GoTo ErrorHandler

    TestSegmentForm = True
    ' 这里可以添加具体的测试逻辑
    Exit Function

ErrorHandler:
    TestSegmentForm = False
End Function

' =============================================================================
' 应用程序级别事件处理器
' =============================================================================

' 应用程序级别的工作表变化事件
Private Sub App_SheetChange(ByVal Sh As Object, ByVal Target As Range)
    On Error GoTo ErrorHandler

    ' 调用统一的事件处理器
    Call EventHandler.HandleWorksheetChange(Sh, Target)

    Exit Sub

ErrorHandler:
    ' 记录错误但不中断用户操作
    On Error Resume Next
    Call LogManager.LogEvent("ERROR", "应用程序级别工作表变化事件处理异常: " & Err.Description, Sh.Name, "事件处理")
End Sub

' 应用程序级别的工作表双击事件
Private Sub App_SheetBeforeDoubleClick(ByVal Sh As Object, ByVal Target As Range, Cancel As Boolean)
    On Error GoTo ErrorHandler

    ' 调用统一的事件处理器
    Call EventHandler.HandleWorksheetDoubleClick(Sh, Target)

    Exit Sub

ErrorHandler:
    ' 记录错误但不中断用户操作
    On Error Resume Next
    Call LogManager.LogEvent("ERROR", "应用程序级别工作表双击事件处理异常: " & Err.Description, Sh.Name, "事件处理")
End Sub




