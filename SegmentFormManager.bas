Option Explicit

' =============================================================================
' 生意板块选择窗体管理模块 - 希尔顿酒店管理系统
' 处理SegmentForm窗体的显示、数据收集和写入操作
' 替代原有的"add more"按钮功能，提供更好的用户体验
' =============================================================================

' 常量定义
Private Const BUSINESS_SHEET As String = "2. 业务背景"

' 模块级变量 - 控制窗体显示时机
Private m_SystemInitializingFlag As Boolean

' 显示生意板块选择窗体
Public Sub ShowSegmentForm()
    On Error GoTo ErrorHandler

    ' 检查系统是否正在初始化，如果是则不显示窗体
    If m_SystemInitializingFlag Then
        Call LogManager.LogEvent("WARNING", "系统初始化期间跳过SegmentForm显示", BUSINESS_SHEET, "窗体管理")
        Exit Sub
    End If

    ' 检查事件是否被禁用，如果是则不显示窗体
    If Not Application.EnableEvents Then
        Call LogManager.LogEvent("WARNING", "事件被禁用期间跳过SegmentForm显示", BUSINESS_SHEET, "窗体管理")
        Exit Sub
    End If

    Call LogManager.LogEvent("INFO", "显示生意板块选择窗体", BUSINESS_SHEET, "窗体管理")

    ' 重置窗体状态
    SegmentForm.ResetForm

    ' 显示窗体（模态显示）
    SegmentForm.Show vbModal

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "显示生意板块选择窗体失败: " & Err.Description, BUSINESS_SHEET, "窗体管理")
End Sub

' 处理窗体确定按钮点击事件
Public Sub HandleFormOkClick()
    On Error GoTo ErrorHandler
    
    Call Utils.SetStatusBar("正在处理生意板块数据...")
    
    ' 获取工作表
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    
    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到工作表: " & BUSINESS_SHEET, BUSINESS_SHEET, "数据写入")
        Call Utils.ClearStatusBar
        Exit Sub
    End If
    
    ' 收集窗体数据
    Dim 业务类型 As String
    Dim 营销漏斗阶段 As String
    Dim 目标客群 As String
    
    Call CollectFormData(业务类型, 营销漏斗阶段, 目标客群)
    
    ' 验证数据
    If 业务类型 = "" Or 营销漏斗阶段 = "" Then
        Call LogManager.LogEvent("WARNING", "业务类型或营销漏斗阶段未选择", BUSINESS_SHEET, "数据验证")
        Call Utils.ClearStatusBar
        MsgBox "请选择业务类型和营销漏斗阶段", vbExclamation, "数据验证"
        Exit Sub
    End If
    
    ' 写入数据到工作表
    Call WriteDataToWorksheet(ws, 业务类型, 营销漏斗阶段, 目标客群)
    
    ' 隐藏窗体
    SegmentForm.Hide
    
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("生意板块数据添加成功")
    Call LogManager.LogEvent("INFO", "生意板块数据添加成功", BUSINESS_SHEET, "数据写入")
    
    Exit Sub
    
ErrorHandler:
    Call Utils.ClearStatusBar
    Call LogManager.LogEvent("ERROR", "处理窗体确定事件失败: " & Err.Description, BUSINESS_SHEET, "窗体处理")
    MsgBox "数据处理失败: " & Err.Description, vbCritical, "错误"
End Sub

' 收集窗体数据
Private Sub CollectFormData(ByRef 业务类型 As String, ByRef 营销漏斗阶段 As String, ByRef 目标客群 As String)
    On Error Resume Next
    
    ' 收集第一层 - 业务类型选择(单选)
    业务类型 = ""
    If SegmentForm.OptionRoom.Value Then 业务类型 = SegmentForm.OptionRoom.Caption
    If SegmentForm.OptionFB.Value Then 业务类型 = SegmentForm.OptionFB.Caption
    If SegmentForm.OptionGME.Value Then 业务类型 = SegmentForm.OptionGME.Caption
    If SegmentForm.OptionOther.Value Then 业务类型 = SegmentForm.OptionOther.Caption
    
    ' 收集第二层 - 营销漏斗阶段(单选)
    营销漏斗阶段 = ""
    If SegmentForm.OptionAwareness.Value Then 营销漏斗阶段 = SegmentForm.OptionAwareness.Caption
    If SegmentForm.OptionConsideration.Value Then 营销漏斗阶段 = SegmentForm.OptionConsideration.Caption
    If SegmentForm.OptionConversion.Value Then 营销漏斗阶段 = SegmentForm.OptionConversion.Caption
    
    ' 收集第三层 - 目标客群(多选)
    Dim 客群列表 As String
    客群列表 = ""
    
    If SegmentForm.CheckBoxBusinessTravelers.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxBusinessTravelers.Caption)
    End If
    If SegmentForm.CheckBoxFamilyTravelers.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxFamilyTravelers.Caption)
    End If
    If SegmentForm.CheckBoxMillennialsGenZ.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxMillennialsGenZ.Caption)
    End If
    If SegmentForm.CheckBoxSeniorTravelers.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxSeniorTravelers.Caption)
    End If
    If SegmentForm.CheckBoxLuxuryMICE.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxLuxuryMICE.Caption)
    End If
    If SegmentForm.CheckBoxConference.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxConference.Caption)
    End If
    If SegmentForm.CheckBoxHiltonHonorsMember.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxHiltonHonorsMember.Caption)
    End If
    If SegmentForm.CheckBoxFoodies.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxFoodies.Caption)
    End If
    If SegmentForm.CheckBoxCouples.Value Then
        客群列表 = AddToList(客群列表, SegmentForm.CheckBoxCouples.Caption)
    End If
    
    目标客群 = 客群列表
    
    Call LogManager.LogEvent("DEBUG", "收集到的数据 - 业务类型: " & 业务类型 & ", 营销漏斗阶段: " & 营销漏斗阶段 & ", 目标客群: " & 目标客群, BUSINESS_SHEET, "数据收集")
End Sub

' 辅助方法：添加到逗号分隔的列表
Private Function AddToList(currentList As String, newItem As String) As String
    If currentList = "" Then
        AddToList = newItem
    Else
        AddToList = currentList & ", " & newItem
    End If
End Function

' 写入数据到工作表
Private Sub WriteDataToWorksheet(ws As Worksheet, 业务类型 As String, 营销漏斗阶段 As String, 目标客群 As String)
    On Error GoTo ErrorHandler
    
    ' 使用Range("B1000").End(xlUp)方法定位最后一行
    Dim lastDataRow As Integer
    lastDataRow = ws.Range("B1000").End(xlUp).Row
    
    ' 在最后一行下方插入新行
    Dim insertRow As Integer
    insertRow = lastDataRow + 1
    
    ' 工作表保护管理
    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)
    
    ' 禁用事件防止递归调用
    Application.EnableEvents = False
    
    ' 插入新行
    ws.Rows(insertRow).Insert Shift:=xlDown

    ' 复制上一行的格式到新行（B到E列）
    If lastDataRow > 0 Then
        ws.Range("B" & lastDataRow & ":E" & lastDataRow).Copy
        ws.Range("B" & insertRow & ":E" & insertRow).PasteSpecial Paste:=xlPasteFormats
        Application.CutCopyMode = False
    End If

    ' 写入数据
    ws.Cells(insertRow, 2).Value = 业务类型        ' B列: 业务类型
    ws.Cells(insertRow, 4).Value = 营销漏斗阶段     ' D列: 营销漏斗阶段
    ws.Cells(insertRow, 5).Value = 目标客群        ' E列: 目标客群

    ' 设置新添加行的标准格式（白色背景、微软雅黑、10号字体）
    Call Utils.SetStandardDataCellFormat(ws, "B" & insertRow & ":E" & insertRow)
    
    ' 重新启用事件
    Application.EnableEvents = True

    ' 恢复工作表保护
    Call Utils.ProtectWorksheet(ws, wasProtected)

    ' 统一光标处理：清除光标选择状态，确保与年度重大事件Add More功能的用户体验一致
    Call Utils.ClearCursorSelection

    Call LogManager.LogEvent("INFO", "数据写入完成，位置: " & insertRow & ", 业务类型: " & 业务类型, BUSINESS_SHEET, "数据写入")
    
    Exit Sub
    
ErrorHandler:
    Application.EnableEvents = True
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "写入数据到工作表失败: " & Err.Description, BUSINESS_SHEET, "数据写入")
    Err.Raise Err.Number, Err.Source, Err.Description
End Sub

' 处理窗体取消按钮点击事件
Public Sub HandleFormCancelClick()
    On Error Resume Next

    Call LogManager.LogEvent("INFO", "用户取消生意板块选择", BUSINESS_SHEET, "窗体管理")

    ' 重置窗体状态
    SegmentForm.ResetForm

    ' 隐藏窗体
    SegmentForm.Hide
End Sub

' 测试方法：直接显示窗体（用于调试）
Public Sub TestShowSegmentForm()
    On Error GoTo ErrorHandler

    Call LogManager.LogEvent("INFO", "测试显示生意板块选择窗体", BUSINESS_SHEET, "测试")

    ' 重置窗体状态
    SegmentForm.ResetForm

    ' 显示窗体
    SegmentForm.Show vbModal

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "测试显示窗体失败: " & Err.Description, BUSINESS_SHEET, "测试")
    MsgBox "测试显示窗体失败: " & Err.Description, vbCritical, "测试错误"
End Sub

' 设置系统初始化状态（防止在初始化期间显示窗体）
Public Sub SetSystemInitializingState(isInitializing As Boolean)
    m_SystemInitializingFlag = isInitializing

    If isInitializing Then
        Call LogManager.LogEvent("INFO", "系统初始化状态已设置，SegmentForm将被禁用", BUSINESS_SHEET, "状态管理")
    Else
        Call LogManager.LogEvent("INFO", "系统初始化完成，SegmentForm已启用", BUSINESS_SHEET, "状态管理")
    End If
End Sub

' 检查系统是否正在初始化
Public Function GetSystemInitializingState() As Boolean
    GetSystemInitializingState = m_SystemInitializingFlag
End Function


