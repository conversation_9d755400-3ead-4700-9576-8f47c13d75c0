Option Explicit

' =============================================================================
' 酒店信息管理模块 - 希尔顿酒店管理系统
' 实现酒店信息的增删改查功能，包括基本信息和USP信息
' 遵循项目代码组织原则：精简代码、复用通用方法、集中Token管理
'
' 主要入口函数：
' - SaveHotelInfoToDB()    : 将Excel数据保存到数据库（主要保存入口）
' - LoadHotelInfoFromDB()  : 从数据库加载数据到Excel（主要加载入口）
'
' 性能优化特性：
' - 批量数据库操作，减少连接次数
' - 批量工作表保护管理，减少保护操作
' - 智能屏幕更新控制，提升用户体验
' - 精简日志记录，仅记录关键信息到Excel
' =============================================================================

' 工作表名称常量
Private Const HOTEL_INFO_SHEET As String = "1.酒店信息"

' 酒店基本信息单元格地址映射
Private Const CELL_ID As String = "A5"
Private Const CELL_INN_CODE As String = "D6"
Private Const CELL_PLAN_OWNER As String = "D5"
Private Const CELL_HOTEL_NAME As String = "D7"
Private Const CELL_REGION As String = "D8"
Private Const CELL_GM_NAME As String = "D9"
Private Const CELL_COM_DIRECTOR As String = "D10"
Private Const CELL_MKT_MGR As String = "D11"
Private Const CELL_MECC_CONTACT As String = "D12"
Private Const CELL_BUDGET_LOCAL As String = "D16"
Private Const CELL_BUDGET_COOP As String = "D17"
Private Const CELL_BUDGET_PMP As String = "D18"
Private Const CELL_BUDGET_TOTAL As String = "D19"

' USP信息单元格地址映射（行范围25-29）
Private Const USP_START_ROW As Integer = 25
Private Const USP_END_ROW As Integer = 29
Private Const USP_COL_ID As String = "A"
Private Const USP_COL_ROOM As String = "C"
Private Const USP_COL_FOOD As String = "D"
Private Const USP_COL_MEETING As String = "E"
Private Const USP_COL_ADDITIONAL As String = "F"
Private Const USP_COL_OTHER As String = "H"

' =============================================================================
' 主要功能函数
' =============================================================================

' 保存酒店信息到数据库（主入口函数 - 性能优化版）
Public Sub SaveHotelInfoToDB()
    On Error GoTo ErrorHandler

    Dim startTime As Double
    startTime = Timer

    ' 性能优化：禁用屏幕更新和自动计算
    Dim originalScreenUpdating As Boolean
    Dim originalCalculation As XlCalculation
    originalScreenUpdating = Application.ScreenUpdating
    originalCalculation = Application.Calculation

    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual

    Call LogManager.StartNewOperationSession("保存酒店信息", HOTEL_INFO_SHEET)
    Call DataHelper.ShowSyncStatus("正在验证酒店信息...")

    ' 数据验证
    If Not ValidateHotelInfoOptimized() Then
        Call DataHelper.ShowSyncStatus("酒店信息验证失败")
        GoTo CleanupAndExit
    End If

    Call DataHelper.ShowSyncStatus("正在保存酒店信息...")

    ' 性能优化：批量保存所有信息
    If Not SaveHotelInfoToDatabase() Then
        Call LogManager.LogEvent("ERROR", "酒店信息保存失败", HOTEL_INFO_SHEET, "数据保存")
        Call DataHelper.ShowSyncStatus("酒店信息保存失败")
        GoTo CleanupAndExit
    End If

    Dim elapsedTime As Double
    elapsedTime = Timer - startTime

    Call DataHelper.ShowSyncStatus("酒店信息保存成功 " & Format(Now, "hh:nn:ss") & " (耗时: " & Format(elapsedTime, "0.0") & "秒)")
    Call LogManager.LogEvent("INFO", "酒店信息保存成功，耗时: " & Format(elapsedTime, "0.0") & "秒", HOTEL_INFO_SHEET, "数据保存")
    Call LogManager.EndCurrentOperationSession("保存酒店信息", "SUCCESS", HOTEL_INFO_SHEET)

CleanupAndExit:
    ' 恢复Excel设置
    Application.ScreenUpdating = originalScreenUpdating
    Application.Calculation = originalCalculation
    Exit Sub

ErrorHandler:
    ' 恢复Excel设置
    Application.ScreenUpdating = originalScreenUpdating
    Application.Calculation = originalCalculation

    Call LogManager.LogEvent("ERROR", "保存酒店信息异常: " & Err.Description, HOTEL_INFO_SHEET, "系统异常")
    Call DataHelper.ShowSyncStatus("保存失败: " & Err.Description)
    Call LogManager.EndCurrentOperationSession("保存酒店信息", "FAILED", HOTEL_INFO_SHEET)
End Sub

' 从数据库加载酒店信息（主入口函数 - 性能优化版）
Public Sub LoadHotelInfoFromDB()
    On Error GoTo ErrorHandler

    Dim startTime As Double
    startTime = Timer

    ' 性能优化：禁用屏幕更新
    Dim originalScreenUpdating As Boolean
    originalScreenUpdating = Application.ScreenUpdating
    Application.ScreenUpdating = False

    Call LogManager.StartNewOperationSession("加载酒店信息", HOTEL_INFO_SHEET)
    Call DataHelper.ShowSyncStatus("正在加载酒店信息...")

    ' 获取当前用户的Inn Code
    Dim currentInnCode As String
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If Trim(currentInnCode) = "" Then
        Call LogManager.LogEvent("ERROR", "无法获取当前用户Inn Code", HOTEL_INFO_SHEET, "权限验证")
        Call DataHelper.ShowSyncStatus("权限验证失败")
        GoTo CleanupAndExit
    End If

    ' 性能优化：批量加载所有信息
    If Not LoadHotelInfoBatch(currentInnCode) Then
        Call LogManager.LogEvent("WARNING", "未找到完整的酒店信息", HOTEL_INFO_SHEET, "数据加载")
    End If

    Dim elapsedTime As Double
    elapsedTime = Timer - startTime

    Call DataHelper.ShowSyncStatus("酒店信息加载完成 " & Format(Now, "hh:nn:ss") & " (耗时: " & Format(elapsedTime, "0.0") & "秒)")
    Call LogManager.LogEvent("INFO", "酒店信息加载完成，耗时: " & Format(elapsedTime, "0.0") & "秒", HOTEL_INFO_SHEET, "数据加载")
    Call LogManager.EndCurrentOperationSession("加载酒店信息", "SUCCESS", HOTEL_INFO_SHEET)

CleanupAndExit:
    ' 恢复Excel设置
    Application.ScreenUpdating = originalScreenUpdating
    Exit Sub

ErrorHandler:
    ' 恢复Excel设置
    Application.ScreenUpdating = originalScreenUpdating

    Call LogManager.LogEvent("ERROR", "加载酒店信息异常: " & Err.Description, HOTEL_INFO_SHEET, "系统异常")
    Call DataHelper.ShowSyncStatus("加载失败: " & Err.Description)
    Call LogManager.EndCurrentOperationSession("加载酒店信息", "FAILED", HOTEL_INFO_SHEET)
End Sub

' =============================================================================
' 数据验证函数
' =============================================================================

' 验证酒店信息完整性
Private Function ValidateHotelInfo() As Boolean
    On Error GoTo ErrorHandler

    ValidateHotelInfo = False
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)

    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到酒店信息工作表", HOTEL_INFO_SHEET, "工作表验证")
        Exit Function
    End If

    ' 必填字段验证数组
    Dim requiredFields As Variant
    Dim fieldNames As Variant
    Dim i As Integer
    Dim cellValue As String
    Dim hasError As Boolean
    Dim errorFields As String
    Dim errorCount As Integer

    requiredFields = Array(CELL_INN_CODE, CELL_PLAN_OWNER, CELL_HOTEL_NAME, CELL_REGION, CELL_GM_NAME, CELL_COM_DIRECTOR, CELL_MKT_MGR, CELL_MECC_CONTACT)
    fieldNames = Array("Inn Code", "计划制定人", "酒店名称", "酒店区域", "总经理", "商务总监", "市场总监/经理", "MECC联系人")

    hasError = False
    errorFields = ""
    errorCount = 0

    ' 性能优化：批量验证，减少工作表保护操作
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If

    ' 逐个验证必填字段
    For i = LBound(requiredFields) To UBound(requiredFields)
        cellValue = Trim(CStr(DataHelper.GetCellValue(ws, CStr(requiredFields(i)))))

        If cellValue = "" Then
            ' 批量标记错误单元格（不使用安全保护管理，减少开销）
            Call MarkCellAsError(ws, CStr(requiredFields(i)))
            errorFields = errorFields & CStr(fieldNames(i)) & ", "
            errorCount = errorCount + 1
            hasError = True
        Else
            ' 批量清除错误标记
            Call ClearCellErrorMark(ws, CStr(requiredFields(i)))
        End If
    Next i

    ' 恢复工作表保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If

    If hasError Then
        ' 性能优化：只记录一条汇总日志而不是每个字段单独记录
        If Len(errorFields) > 2 Then errorFields = Left(errorFields, Len(errorFields) - 2)
        Call LogManager.LogEvent("WARNING", "数据验证失败，" & errorCount & "个必填字段为空: " & errorFields, HOTEL_INFO_SHEET, "数据验证")
        MsgBox "数据验证失败！请填写所有必填字段（已用红色标记）。", vbExclamation, "数据验证"
        ValidateHotelInfo = False
    Else
        ValidateHotelInfo = True
    End If

    Exit Function

ErrorHandler:
    ' 确保恢复工作表保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If

    Call LogManager.LogEvent("ERROR", "数据验证异常: " & Err.Description, HOTEL_INFO_SHEET, "验证异常")
    ValidateHotelInfo = False
End Function



' 标记单元格为错误状态（假设工作表已解除保护）
Private Sub MarkCellAsError(ws As Worksheet, cellAddress As String)
    On Error Resume Next

    ' 应用错误样式（使用统一的样式常量）
    With ws.Range(cellAddress)
        .Interior.Color = Utils.ERROR_BACKGROUND_COLOR
        .Font.Color = Utils.ERROR_FONT_COLOR
        ' 简化边框设置，只设置主要边框
        .BorderAround Color:=Utils.ERROR_BORDER_COLOR, LineStyle:=xlContinuous, Weight:=xlMedium
    End With
End Sub

' 清除单元格错误标记（假设工作表已解除保护）
Private Sub ClearCellErrorMark(ws As Worksheet, cellAddress As String)
    On Error Resume Next

    With ws.Range(cellAddress)
        .Interior.ColorIndex = xlNone
        .Font.ColorIndex = xlAutomatic
        .BorderAround LineStyle:=xlNone
    End With
End Sub



' =============================================================================
' 数据库操作函数（性能优化版）
' =============================================================================

' 保存酒店信息到数据库（内部实现）
Private Function SaveHotelInfoToDatabase() As Boolean
    On Error GoTo ErrorHandler

    SaveHotelInfoToDatabase = False
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)

    If ws Is Nothing Then Exit Function

    ' 读取所有数据
    Dim hotelData As HotelInfoData
    Dim uspData As HotelUSPData
    Call ReadHotelBasicInfoFromSheet(ws, hotelData)
    Call ReadHotelUSPInfoFromSheet(ws, uspData)

    ' 获取当前Inn Code
    uspData.InnCode = TokenManager.GetCurrentHotelInncode()

    ' 生成或获取ID（批量处理）
    If Trim(hotelData.Id) = "" Then
        hotelData.Id = GenerateHotelInfoID()
    End If

    ' 为USP数据生成ID
    Dim i As Integer
    For i = 1 To 5
        If Trim(uspData.Rows(i).Id) = "" Then
            uspData.Rows(i).Id = GenerateUSPID()
        End If
        uspData.Rows(i).InnCode = uspData.InnCode
        uspData.Rows(i).RowIndex = i
    Next i

    ' 分别执行SQL语句（避免ODBC驱动的多语句问题）
    Dim hotelSQL As String
    Dim uspDeleteSQL As String
    Dim uspInsertSQL As String
    Dim success As Boolean

    hotelSQL = BuildHotelInfoInsertSQL(hotelData)

    ' 构建USP删除和插入SQL
    uspDeleteSQL = "DELETE FROM hotel_usp WHERE inn_code = '" & DataHelper.EscapeSQL(uspData.InnCode) & "'"
    uspInsertSQL = BuildHotelUSPInsertOnlySQL(uspData)

    success = True

    ' 执行酒店基本信息保存
    If Not DabaseCore.ExecuteCommand(hotelSQL) Then
        Call LogManager.LogEvent("ERROR", "酒店基本信息保存失败", HOTEL_INFO_SHEET, "数据保存")
        success = False
    End If

    ' 执行USP数据删除
    If success And Not DabaseCore.ExecuteCommand(uspDeleteSQL) Then
        Call LogManager.LogEvent("ERROR", "USP数据删除失败", HOTEL_INFO_SHEET, "数据保存")
        success = False
    End If

    ' 执行USP数据插入
    If success And Not DabaseCore.ExecuteCommand(uspInsertSQL) Then
        Call LogManager.LogEvent("ERROR", "USP数据插入失败", HOTEL_INFO_SHEET, "数据保存")
        success = False
    End If

    If success Then
        ' 批量更新工作表中的ID（减少保护操作）
        Call UpdateWorksheetIDsBatch(ws, hotelData, uspData)

        Call LogManager.LogEvent("INFO", "酒店信息批量保存成功", HOTEL_INFO_SHEET, "数据保存")
        SaveHotelInfoToDatabase = True
    Else
        Call LogManager.LogEvent("ERROR", "酒店信息批量保存失败", HOTEL_INFO_SHEET, "数据保存")
    End If

    Exit Function

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "批量保存酒店信息异常: " & Err.Description, HOTEL_INFO_SHEET, "保存异常")
    SaveHotelInfoToDatabase = False
End Function

' 批量更新工作表中的ID
Private Sub UpdateWorksheetIDsBatch(ws As Worksheet, hotelData As HotelInfoData, uspData As HotelUSPData)
    On Error Resume Next

    ' 性能优化：一次性解除保护，批量更新，然后恢复保护
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If

    ' 更新酒店基本信息ID
    ws.Range(CELL_ID).Value = hotelData.Id

    ' 批量更新USP ID
    Dim i As Integer
    For i = 1 To 5
        ws.Range(USP_COL_ID & (USP_START_ROW + i - 1)).Value = uspData.Rows(i).Id
    Next i

    ' 恢复保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If
End Sub

' 从数据库加载酒店信息（内部实现）
Private Function LoadHotelInfoFromDatabase(innCode As String) As Boolean
    On Error GoTo ErrorHandler

    LoadHotelInfoFromDatabase = False
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)

    If ws Is Nothing Then Exit Function

    ' 性能优化：一次性解除保护，批量更新，然后恢复保护
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If

    ' 加载基本信息
    Dim basicLoaded As Boolean
    basicLoaded = LoadHotelBasicInfoFast(ws, innCode)

    ' 加载USP信息
    Dim uspLoaded As Boolean
    uspLoaded = LoadHotelUSPInfoFast(ws, innCode)

    ' 恢复保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If

    LoadHotelInfoFromDatabase = (basicLoaded Or uspLoaded)
    Exit Function

ErrorHandler:
    ' 确保恢复工作表保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If

    Call LogManager.LogEvent("ERROR", "批量加载酒店信息异常: " & Err.Description, HOTEL_INFO_SHEET, "加载异常")
    LoadHotelInfoFromDatabase = False
End Function

' 快速加载酒店基本信息（假设工作表已解除保护）
Private Function LoadHotelBasicInfoFast(ws As Worksheet, innCode As String) As Boolean
    On Error GoTo ErrorHandler

    LoadHotelBasicInfoFast = False

    ' 构建查询SQL
    Dim sql As String
    sql = "SELECT * FROM hotel_info WHERE inn_code = '" & DataHelper.EscapeSQL(innCode) & "' LIMIT 1"

    ' 执行查询
    Dim rs As Object
    Set rs = DabaseCore.ExecuteQuery(sql)

    If Not rs Is Nothing Then
        If Not rs.EOF Then
            ' 批量写入数据（不使用安全保护管理，减少开销）
            ws.Range(CELL_ID).Value = SafeFieldValue(rs.Fields("id"))
            ws.Range(CELL_PLAN_OWNER).Value = SafeFieldValue(rs.Fields("plan_owner"))
            ws.Range(CELL_INN_CODE).Value = SafeFieldValue(rs.Fields("inn_code"))
            ws.Range(CELL_HOTEL_NAME).Value = SafeFieldValue(rs.Fields("hotel_name"))
            ws.Range(CELL_REGION).Value = SafeFieldValue(rs.Fields("region"))
            ws.Range(CELL_GM_NAME).Value = SafeFieldValue(rs.Fields("gm_name"))
            ws.Range(CELL_COM_DIRECTOR).Value = SafeFieldValue(rs.Fields("com_director"))
            ws.Range(CELL_MKT_MGR).Value = SafeFieldValue(rs.Fields("mkt_mgr"))
            ws.Range(CELL_MECC_CONTACT).Value = SafeFieldValue(rs.Fields("mecc_contact"))
            ws.Range(CELL_BUDGET_LOCAL).Value = SafeFieldValue(rs.Fields("budget_local"))
            ws.Range(CELL_BUDGET_COOP).Value = SafeFieldValue(rs.Fields("budget_coop"))
            ws.Range(CELL_BUDGET_PMP).Value = SafeFieldValue(rs.Fields("budget_pmp"))
            ws.Range(CELL_BUDGET_TOTAL).Value = SafeFieldValue(rs.Fields("budget_total"))

            LoadHotelBasicInfoFast = True
        End If
        rs.Close
    End If

    Exit Function

ErrorHandler:
    LoadHotelBasicInfoFast = False
End Function

' 从数据库加载酒店基本信息（保留原版本以兼容）
Private Function LoadHotelBasicInfo(innCode As String) As Boolean
    LoadHotelBasicInfo = LoadHotelInfoBatch(innCode)
End Function

' 快速加载酒店USP信息（假设工作表已解除保护）
Private Function LoadHotelUSPInfoFast(ws As Worksheet, innCode As String) As Boolean
    On Error GoTo ErrorHandler

    LoadHotelUSPInfoFast = False

    ' 构建查询SQL
    Dim sql As String
    sql = "SELECT * FROM hotel_usp WHERE inn_code = '" & DataHelper.EscapeSQL(innCode) & "' ORDER BY row_index"

    ' 执行查询
    Dim rs As Object
    Set rs = DabaseCore.ExecuteQuery(sql)

    If Not rs Is Nothing Then
        Dim rowIndex As Integer
        rowIndex = 1

        ' 快速清空现有USP数据（不使用安全保护管理）
        Call ClearUSPDataFast(ws)

        ' 批量填充数据
        Do While Not rs.EOF And rowIndex <= 5
            Dim currentRow As Integer
            currentRow = USP_START_ROW + rowIndex - 1

            ws.Range(USP_COL_ID & currentRow).Value = SafeFieldValue(rs.Fields("id"))
            ws.Range(USP_COL_ROOM & currentRow).Value = SafeFieldValue(rs.Fields("room"))
            ws.Range(USP_COL_FOOD & currentRow).Value = SafeFieldValue(rs.Fields("food"))
            ws.Range(USP_COL_MEETING & currentRow).Value = SafeFieldValue(rs.Fields("meeting"))
            ws.Range(USP_COL_ADDITIONAL & currentRow).Value = SafeFieldValue(rs.Fields("additional"))
            ws.Range(USP_COL_OTHER & currentRow).Value = SafeFieldValue(rs.Fields("other_service"))

            rs.MoveNext
            rowIndex = rowIndex + 1
        Loop

        rs.Close
        LoadHotelUSPInfoFast = True
    End If

    Exit Function

ErrorHandler:
    LoadHotelUSPInfoFast = False
End Function

' 从数据库加载酒店USP信息（保留原版本以兼容）
Private Function LoadHotelUSPInfo(innCode As String) As Boolean
    On Error GoTo ErrorHandler

    LoadHotelUSPInfo = False
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)

    If ws Is Nothing Then Exit Function

    ' 使用批量加载方式
    LoadHotelUSPInfo = LoadHotelUSPInfoFast(ws, innCode)
    Exit Function

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "加载酒店USP信息异常: " & Err.Description, HOTEL_INFO_SHEET, "加载异常")
    LoadHotelUSPInfo = False
End Function

' =============================================================================
' 数据读取和处理函数
' =============================================================================

' 从工作表读取酒店基本信息
Private Sub ReadHotelBasicInfoFromSheet(ws As Worksheet, ByRef hotelData As HotelInfoData)
    On Error Resume Next

    hotelData.Id = Trim(CStr(DataHelper.GetCellValue(ws, CELL_ID)))
    hotelData.PlanOwner = Trim(CStr(DataHelper.GetCellValue(ws, CELL_PLAN_OWNER)))
    hotelData.Inncode = Trim(CStr(DataHelper.GetCellValue(ws, CELL_INN_CODE)))
    hotelData.HotelName = Trim(CStr(DataHelper.GetCellValue(ws, CELL_HOTEL_NAME)))
    hotelData.Region = Trim(CStr(DataHelper.GetCellValue(ws, CELL_REGION)))
    hotelData.GMName = Trim(CStr(DataHelper.GetCellValue(ws, CELL_GM_NAME)))
    hotelData.ComDirector = Trim(CStr(DataHelper.GetCellValue(ws, CELL_COM_DIRECTOR)))
    hotelData.MktMgr = Trim(CStr(DataHelper.GetCellValue(ws, CELL_MKT_MGR)))
    hotelData.MECCContact = Trim(CStr(DataHelper.GetCellValue(ws, CELL_MECC_CONTACT)))
    hotelData.BudgetLocal = Trim(CStr(DataHelper.GetCellValue(ws, CELL_BUDGET_LOCAL)))
    hotelData.BudgetCoop = Trim(CStr(DataHelper.GetCellValue(ws, CELL_BUDGET_COOP)))
    hotelData.BudgetPMP = Trim(CStr(DataHelper.GetCellValue(ws, CELL_BUDGET_PMP)))
    hotelData.BudgetTotal = Trim(CStr(DataHelper.GetCellValue(ws, CELL_BUDGET_TOTAL)))
End Sub

' 从工作表读取酒店USP信息
Private Sub ReadHotelUSPInfoFromSheet(ws As Worksheet, ByRef uspData As HotelUSPData)
    On Error Resume Next

    Dim i As Integer
    For i = 1 To 5
        Dim currentRow As Integer
        currentRow = USP_START_ROW + i - 1

        uspData.Rows(i).Id = Trim(CStr(DataHelper.GetCellValue(ws, USP_COL_ID & currentRow)))
        uspData.Rows(i).Room = Trim(CStr(DataHelper.GetCellValue(ws, USP_COL_ROOM & currentRow)))
        uspData.Rows(i).Food = Trim(CStr(DataHelper.GetCellValue(ws, USP_COL_FOOD & currentRow)))
        uspData.Rows(i).Meeting = Trim(CStr(DataHelper.GetCellValue(ws, USP_COL_MEETING & currentRow)))
        uspData.Rows(i).Additional = Trim(CStr(DataHelper.GetCellValue(ws, USP_COL_ADDITIONAL & currentRow)))
        uspData.Rows(i).OtherService = Trim(CStr(DataHelper.GetCellValue(ws, USP_COL_OTHER & currentRow)))
    Next i
End Sub

' 快速清空USP数据区域（假设工作表已解除保护）
Private Sub ClearUSPDataFast(ws As Worksheet)
    On Error Resume Next

    Dim i As Integer
    For i = USP_START_ROW To USP_END_ROW
        ws.Range(USP_COL_ID & i).Value = ""
        ws.Range(USP_COL_ROOM & i).Value = ""
        ws.Range(USP_COL_FOOD & i).Value = ""
        ws.Range(USP_COL_MEETING & i).Value = ""
        ws.Range(USP_COL_ADDITIONAL & i).Value = ""
        ws.Range(USP_COL_OTHER & i).Value = ""
    Next i
End Sub

' 清空USP数据区域（保留原版本以兼容）
Private Sub ClearUSPData(ws As Worksheet)
    On Error Resume Next

    Dim i As Integer
    For i = USP_START_ROW To USP_END_ROW
        Call WorksheetProtectionManager.SafeSetCellValue(ws, USP_COL_ID & i, "")
        Call WorksheetProtectionManager.SafeSetCellValue(ws, USP_COL_ROOM & i, "")
        Call WorksheetProtectionManager.SafeSetCellValue(ws, USP_COL_FOOD & i, "")
        Call WorksheetProtectionManager.SafeSetCellValue(ws, USP_COL_MEETING & i, "")
        Call WorksheetProtectionManager.SafeSetCellValue(ws, USP_COL_ADDITIONAL & i, "")
        Call WorksheetProtectionManager.SafeSetCellValue(ws, USP_COL_OTHER & i, "")
    Next i
End Sub

' =============================================================================
' SQL构建函数
' =============================================================================

' 构建酒店基本信息插入SQL
Private Function BuildHotelInfoInsertSQL(hotelData As HotelInfoData) As String
    On Error Resume Next

    Dim sql As String

    ' 构建INSERT部分
    sql = "INSERT INTO hotel_info "
    sql = sql & "(id, inn_code, plan_owner, hotel_name, region, gm_name, com_director, mkt_mgr, mecc_contact, "
    sql = sql & "budget_local, budget_coop, budget_pmp, budget_total, created_at, updated_at) VALUES ("

    ' 构建VALUES部分
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.Id) & "', "
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.Inncode) & "', "
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.PlanOwner) & "', "
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.HotelName) & "', "
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.Region) & "', "
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.GMName) & "', "
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.ComDirector) & "', "
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.MktMgr) & "', "
    sql = sql & "'" & DataHelper.EscapeSQL(hotelData.MECCContact) & "', "
    sql = sql & ConvertToDecimal(hotelData.BudgetLocal) & ", "
    sql = sql & ConvertToDecimal(hotelData.BudgetCoop) & ", "
    sql = sql & ConvertToDecimal(hotelData.BudgetPMP) & ", "
    sql = sql & ConvertToDecimal(hotelData.BudgetTotal) & ", "
    sql = sql & "NOW(), NOW()) "

    ' 构建ON DUPLICATE KEY UPDATE部分
    sql = sql & "ON DUPLICATE KEY UPDATE "
    sql = sql & "plan_owner = VALUES(plan_owner), "
    sql = sql & "hotel_name = VALUES(hotel_name), "
    sql = sql & "region = VALUES(region), "
    sql = sql & "gm_name = VALUES(gm_name), "
    sql = sql & "com_director = VALUES(com_director), "
    sql = sql & "mkt_mgr = VALUES(mkt_mgr), "
    sql = sql & "mecc_contact = VALUES(mecc_contact), "
    sql = sql & "budget_local = VALUES(budget_local), "
    sql = sql & "budget_coop = VALUES(budget_coop), "
    sql = sql & "budget_pmp = VALUES(budget_pmp), "
    sql = sql & "budget_total = VALUES(budget_total), "
    sql = sql & "updated_at = NOW()"

    BuildHotelInfoInsertSQL = sql
End Function

' 构建酒店USP信息仅插入SQL（不包含DELETE）
Private Function BuildHotelUSPInsertOnlySQL(uspData As HotelUSPData) As String
    On Error Resume Next

    Dim sql As String
    Dim i As Integer
    Dim valuesParts As String

    ' 构建批量插入SQL（使用单个INSERT语句插入多行）
    sql = "INSERT INTO hotel_usp "
    sql = sql & "(id, inn_code, row_index, room, food, meeting, additional, other_service, created_at, updated_at) VALUES "

    ' 构建VALUES部分
    valuesParts = ""
    For i = 1 To 5
        If i > 1 Then valuesParts = valuesParts & ", "
        valuesParts = valuesParts & "("
        valuesParts = valuesParts & "'" & DataHelper.EscapeSQL(uspData.Rows(i).Id) & "', "
        valuesParts = valuesParts & "'" & DataHelper.EscapeSQL(uspData.InnCode) & "', "
        valuesParts = valuesParts & i & ", "
        valuesParts = valuesParts & "'" & DataHelper.EscapeSQL(uspData.Rows(i).Room) & "', "
        valuesParts = valuesParts & "'" & DataHelper.EscapeSQL(uspData.Rows(i).Food) & "', "
        valuesParts = valuesParts & "'" & DataHelper.EscapeSQL(uspData.Rows(i).Meeting) & "', "
        valuesParts = valuesParts & "'" & DataHelper.EscapeSQL(uspData.Rows(i).Additional) & "', "
        valuesParts = valuesParts & "'" & DataHelper.EscapeSQL(uspData.Rows(i).OtherService) & "', "
        valuesParts = valuesParts & "NOW(), NOW()"
        valuesParts = valuesParts & ")"
    Next i

    sql = sql & valuesParts

    BuildHotelUSPInsertOnlySQL = sql
End Function

' 构建酒店USP信息批量插入SQL（性能优化版 - 包含DELETE）
Private Function BuildHotelUSPBatchInsertSQL(uspData As HotelUSPData) As String
    On Error Resume Next

    Dim sql As String

    ' 先删除现有数据，然后插入新数据
    sql = "DELETE FROM hotel_usp WHERE inn_code = '" & DataHelper.EscapeSQL(uspData.InnCode) & "'; "
    sql = sql & BuildHotelUSPInsertOnlySQL(uspData)

    BuildHotelUSPBatchInsertSQL = sql
End Function

' 构建酒店USP信息插入SQL（保留原版本以兼容）
Private Function BuildHotelUSPInsertSQL(uspData As HotelUSPData) As String
    BuildHotelUSPInsertSQL = BuildHotelUSPBatchInsertSQL(uspData)
End Function

' =============================================================================
' 辅助函数
' =============================================================================

' 安全获取数据库字段值
Private Function SafeFieldValue(field As Object) As String
    On Error Resume Next

    If IsNull(field.Value) Then
        SafeFieldValue = ""
    Else
        SafeFieldValue = CStr(field.Value)
    End If

    If Err.Number <> 0 Then
        SafeFieldValue = ""
        Err.Clear
    End If
End Function

' 生成酒店信息ID
Private Function GenerateHotelInfoID() As String
    GenerateHotelInfoID = "HOTEL-" & Format(Now, "yyyymmddhhnnss") & "-" & Format(Int(Rnd() * 10000), "0000")
End Function

' 生成USP ID
Private Function GenerateUSPID() As String
    GenerateUSPID = "USP-" & Format(Now, "yyyymmddhhnnss") & "-" & Format(Int(Rnd() * 10000), "0000")
End Function

' 转换预算数据为数据库格式
Private Function ConvertToDecimal(budgetValue As String) As String
    On Error Resume Next

    Dim cleanValue As String
    cleanValue = Trim(budgetValue)

    ' 移除货币符号和逗号
    cleanValue = Replace(cleanValue, ",", "")
    cleanValue = Replace(cleanValue, "¥", "")
    cleanValue = Replace(cleanValue, "$", "")
    cleanValue = Replace(cleanValue, "￥", "")

    ' 检查是否为数字
    If IsNumeric(cleanValue) And cleanValue <> "" Then
        ConvertToDecimal = cleanValue
    Else
        ConvertToDecimal = "0"
    End If
End Function

' =============================================================================
' 测试函数
' =============================================================================

' SQL语句测试函数
Public Sub TestSQLGeneration()
    On Error GoTo ErrorHandler

    Call LogManager.StartNewOperationSession("SQL生成测试", HOTEL_INFO_SHEET)

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)

    If ws Is Nothing Then
        MsgBox "找不到酒店信息工作表", vbCritical
        Exit Sub
    End If

    ' 读取数据
    Dim hotelData As HotelInfoData
    Dim uspData As HotelUSPData
    Call ReadHotelBasicInfoFromSheet(ws, hotelData)
    Call ReadHotelUSPInfoFromSheet(ws, uspData)

    ' 设置测试数据
    If Trim(hotelData.Id) = "" Then hotelData.Id = GenerateHotelInfoID()
    uspData.InnCode = TokenManager.GetCurrentHotelInncode()

    Dim i As Integer
    For i = 1 To 5
        If Trim(uspData.Rows(i).Id) = "" Then uspData.Rows(i).Id = GenerateUSPID()
        uspData.Rows(i).InnCode = uspData.InnCode
        uspData.Rows(i).RowIndex = i
    Next i

    ' 生成SQL语句
    Dim hotelSQL As String
    Dim uspInsertSQL As String
    Dim uspDeleteSQL As String

    hotelSQL = BuildHotelInfoInsertSQL(hotelData)
    uspInsertSQL = BuildHotelUSPInsertOnlySQL(uspData)
    uspDeleteSQL = "DELETE FROM hotel_usp WHERE inn_code = '" & DataHelper.EscapeSQL(uspData.InnCode) & "'"

    ' 显示SQL语句用于调试
    Dim msg As String
    msg = "=== SQL语句生成测试 ===" & vbCrLf & vbCrLf
    msg = msg & "酒店信息SQL长度: " & Len(hotelSQL) & vbCrLf
    msg = msg & "USP删除SQL长度: " & Len(uspDeleteSQL) & vbCrLf
    msg = msg & "USP插入SQL长度: " & Len(uspInsertSQL) & vbCrLf & vbCrLf

    ' 检查SQL语句是否包含关键字
    If InStr(hotelSQL, "INSERT INTO hotel_info") > 0 Then
        msg = msg & "✓ 酒店信息SQL格式正确" & vbCrLf
    Else
        msg = msg & "✗ 酒店信息SQL格式错误" & vbCrLf
    End If

    If InStr(uspInsertSQL, "INSERT INTO hotel_usp") > 0 Then
        msg = msg & "✓ USP插入SQL格式正确" & vbCrLf
    Else
        msg = msg & "✗ USP插入SQL格式错误" & vbCrLf
    End If

    If InStr(uspDeleteSQL, "DELETE FROM hotel_usp") > 0 Then
        msg = msg & "✓ USP删除SQL格式正确" & vbCrLf
    Else
        msg = msg & "✗ USP删除SQL格式错误" & vbCrLf
    End If

    MsgBox msg, vbInformation, "SQL生成测试完成"

    Call LogManager.LogEvent("INFO", "SQL生成测试完成", HOTEL_INFO_SHEET, "SQL测试")
    Call LogManager.EndCurrentOperationSession("SQL生成测试", "SUCCESS", HOTEL_INFO_SHEET)

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "SQL生成测试异常: " & Err.Description, HOTEL_INFO_SHEET, "测试异常")
    Call LogManager.EndCurrentOperationSession("SQL生成测试", "FAILED", HOTEL_INFO_SHEET)
    MsgBox "SQL测试过程中发生异常: " & Err.Description, vbCritical, "测试异常"
End Sub

' 性能测试酒店信息管理功能
Public Sub TestHotelInfoManagerPerformance()
    On Error GoTo ErrorHandler

    Call LogManager.StartNewOperationSession("酒店信息管理性能测试", HOTEL_INFO_SHEET)

    Dim testResults As String
    Dim passCount As Integer
    Dim totalTests As Integer
    Dim startTime As Double
    Dim elapsedTime As Double

    testResults = "=== 酒店信息管理性能测试报告 ===" & vbCrLf & vbCrLf
    passCount = 0
    totalTests = 0

    ' 测试1: 保存性能测试
    totalTests = totalTests + 1
    testResults = testResults & "测试1: 保存操作性能" & vbCrLf
    startTime = Timer

    ' 执行保存操作
    Call SaveHotelInfoToDB

    elapsedTime = Timer - startTime
    testResults = testResults & "保存耗时: " & Format(elapsedTime, "0.00") & "秒"

    If elapsedTime < 5 Then
        testResults = testResults & " ✓ 性能良好" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & " ⚠️ 性能需要优化" & vbCrLf
    End If

    ' 测试2: 加载性能测试
    totalTests = totalTests + 1
    testResults = testResults & vbCrLf & "测试2: 加载操作性能" & vbCrLf
    startTime = Timer

    ' 执行加载操作
    Call LoadHotelInfoFromDB

    elapsedTime = Timer - startTime
    testResults = testResults & "加载耗时: " & Format(elapsedTime, "0.00") & "秒"

    If elapsedTime < 3 Then
        testResults = testResults & " ✓ 性能良好" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & " ⚠️ 性能需要优化" & vbCrLf
    End If

    ' 测试3: 数据库连接测试
    totalTests = totalTests + 1
    If DabaseCore.TestConnection() Then
        testResults = testResults & vbCrLf & "✓ 数据库连接正常" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & vbCrLf & "✗ 数据库连接失败" & vbCrLf
    End If

    ' 汇总结果
    testResults = testResults & vbCrLf & "=== 性能测试汇总 ===" & vbCrLf
    testResults = testResults & "通过测试: " & passCount & "/" & totalTests & vbCrLf
    testResults = testResults & "成功率: " & Format((passCount / totalTests) * 100, "0.0") & "%" & vbCrLf & vbCrLf

    If passCount = totalTests Then
        testResults = testResults & "🎉 所有性能测试通过！系统运行良好。"
    Else
        testResults = testResults & "⚠️ 部分性能测试未通过，建议进一步优化。"
    End If

    ' 显示测试结果
    MsgBox testResults, vbInformation, "性能测试完成"

    Call LogManager.LogEvent("INFO", "性能测试完成 - 通过率: " & Format((passCount / totalTests) * 100, "0.0") & "%", HOTEL_INFO_SHEET, "性能测试")
    Call LogManager.EndCurrentOperationSession("酒店信息管理性能测试", "SUCCESS", HOTEL_INFO_SHEET)

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "性能测试异常: " & Err.Description, HOTEL_INFO_SHEET, "测试异常")
    Call LogManager.EndCurrentOperationSession("酒店信息管理性能测试", "FAILED", HOTEL_INFO_SHEET)
    MsgBox "性能测试过程中发生异常: " & Err.Description, vbCritical, "测试异常"
End Sub

' 综合测试酒店信息管理功能
Public Sub TestHotelInfoManager()
    On Error GoTo ErrorHandler

    Call LogManager.StartNewOperationSession("酒店信息管理测试", HOTEL_INFO_SHEET)

    Dim testResults As String
    Dim passCount As Integer
    Dim totalTests As Integer

    testResults = "=== 酒店信息管理模块测试报告 ===" & vbCrLf & vbCrLf
    passCount = 0
    totalTests = 0

    ' 测试1: 工作表访问
    totalTests = totalTests + 1
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)
    If Not ws Is Nothing Then
        testResults = testResults & "✓ 工作表访问正常" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & "✗ 工作表访问失败" & vbCrLf
    End If

    ' 测试2: Token管理
    totalTests = totalTests + 1
    Dim currentInnCode As String
    currentInnCode = TokenManager.GetCurrentHotelInncode()
    If Trim(currentInnCode) <> "" Then
        testResults = testResults & "✓ Token管理正常 (Inn Code: " & currentInnCode & ")" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & "✗ Token管理异常" & vbCrLf
    End If

    ' 测试3: 数据库连接
    totalTests = totalTests + 1
    If DabaseCore.TestConnection() Then
        testResults = testResults & "✓ 数据库连接正常" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & "✗ 数据库连接失败" & vbCrLf
    End If

    ' 测试4: 数据验证功能
    totalTests = totalTests + 1
    If TestDataValidation() Then
        testResults = testResults & "✓ 数据验证功能正常" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & "✗ 数据验证功能异常" & vbCrLf
    End If

    ' 测试5: ID生成功能
    totalTests = totalTests + 1
    Dim testID As String
    testID = GenerateHotelInfoID()
    If Len(testID) > 10 And InStr(testID, "HOTEL-") > 0 Then
        testResults = testResults & "✓ ID生成功能正常 (示例: " & testID & ")" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & "✗ ID生成功能异常" & vbCrLf
    End If

    ' 测试6: 数据转换功能
    totalTests = totalTests + 1
    If TestDataConversion() Then
        testResults = testResults & "✓ 数据转换功能正常" & vbCrLf
        passCount = passCount + 1
    Else
        testResults = testResults & "✗ 数据转换功能异常" & vbCrLf
    End If

    ' 汇总结果
    testResults = testResults & vbCrLf & "=== 测试汇总 ===" & vbCrLf
    testResults = testResults & "通过测试: " & passCount & "/" & totalTests & vbCrLf
    testResults = testResults & "成功率: " & Format((passCount / totalTests) * 100, "0.0") & "%" & vbCrLf & vbCrLf

    If passCount = totalTests Then
        testResults = testResults & "🎉 所有测试通过！酒店信息管理模块已准备就绪。" & vbCrLf & vbCrLf
        testResults = testResults & "可以使用以下功能：" & vbCrLf
        testResults = testResults & "• SaveHotelInfoToDB() - 保存酒店信息到数据库" & vbCrLf
        testResults = testResults & "• LoadHotelInfoFromDB() - 从数据库加载酒店信息"
    Else
        testResults = testResults & "⚠️ 部分测试失败，请检查相关功能。"
    End If

    ' 显示测试结果
    MsgBox testResults, vbInformation, "酒店信息管理测试完成"

    Call LogManager.LogEvent("INFO", "酒店信息管理测试完成 - 通过率: " & Format((passCount / totalTests) * 100, "0.0") & "%", HOTEL_INFO_SHEET, "功能测试")
    Call LogManager.EndCurrentOperationSession("酒店信息管理测试", "SUCCESS", HOTEL_INFO_SHEET)

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "酒店信息管理测试异常: " & Err.Description, HOTEL_INFO_SHEET, "测试异常")
    Call LogManager.EndCurrentOperationSession("酒店信息管理测试", "FAILED", HOTEL_INFO_SHEET)
    MsgBox "测试过程中发生异常: " & Err.Description, vbCritical, "测试异常"
End Sub




' 清除所有错误标记
Public Sub ClearAllErrorMarks()
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)

    If ws Is Nothing Then
        MsgBox "错误：找不到工作表 '1.酒店信息'", vbCritical
        Exit Sub
    End If

    Dim requiredFields As Variant
    requiredFields = Array(CELL_INN_CODE, CELL_PLAN_OWNER, CELL_HOTEL_NAME, CELL_REGION, CELL_GM_NAME, CELL_COM_DIRECTOR, CELL_MKT_MGR, CELL_MECC_CONTACT)

    Dim i As Integer
    For i = LBound(requiredFields) To UBound(requiredFields)
        Call ClearCellErrorMark(ws, CStr(requiredFields(i)))
    Next i

    MsgBox "已清除所有错误标记", vbInformation, "清除完成"
End Sub

' =============================================================================
' 缺失函数实现
' =============================================================================

' 优化版数据验证函数
Private Function ValidateHotelInfoOptimized() As Boolean
    On Error GoTo ErrorHandler

    ValidateHotelInfoOptimized = False
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)

    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到酒店信息工作表", HOTEL_INFO_SHEET, "工作表验证")
        Exit Function
    End If

    ' 必填字段验证数组
    Dim requiredFields As Variant
    Dim fieldNames As Variant
    Dim i As Integer
    Dim cellValue As String
    Dim hasError As Boolean
    Dim errorFields As String
    Dim errorCount As Integer

    requiredFields = Array(CELL_INN_CODE, CELL_PLAN_OWNER, CELL_HOTEL_NAME, CELL_REGION, CELL_GM_NAME, CELL_COM_DIRECTOR, CELL_MKT_MGR, CELL_MECC_CONTACT)
    fieldNames = Array("Inn Code", "计划制定人", "酒店名称", "酒店区域", "总经理", "商务总监", "市场总监/经理", "MECC联系人")

    hasError = False
    errorFields = ""
    errorCount = 0

    ' 性能优化：批量验证，减少工作表保护操作
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If

    ' 逐个验证必填字段
    For i = LBound(requiredFields) To UBound(requiredFields)
        cellValue = Trim(CStr(DataHelper.GetCellValue(ws, CStr(requiredFields(i)))))

        If cellValue = "" Then
            ' 批量标记错误单元格
            Call MarkCellAsError(ws, CStr(requiredFields(i)))
            errorFields = errorFields & CStr(fieldNames(i)) & ", "
            errorCount = errorCount + 1
            hasError = True
        Else
            ' 批量清除错误标记
            Call ClearCellErrorMark(ws, CStr(requiredFields(i)))
        End If
    Next i

    ' 恢复工作表保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If

    If hasError Then
        ' 性能优化：只记录一条汇总日志
        If Len(errorFields) > 2 Then errorFields = Left(errorFields, Len(errorFields) - 2)
        Call LogManager.LogEvent("WARNING", "数据验证失败，" & errorCount & "个必填字段为空: " & errorFields, HOTEL_INFO_SHEET, "数据验证")
        ValidateHotelInfoOptimized = False
    Else
        ValidateHotelInfoOptimized = True
    End If

    Exit Function

ErrorHandler:
    ' 确保恢复工作表保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If

    Call LogManager.LogEvent("ERROR", "数据验证异常: " & Err.Description, HOTEL_INFO_SHEET, "验证异常")
    ValidateHotelInfoOptimized = False
End Function

' 批量加载酒店信息函数
Private Function LoadHotelInfoBatch(innCode As String) As Boolean
    On Error GoTo ErrorHandler

    LoadHotelInfoBatch = False
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(HOTEL_INFO_SHEET)

    If ws Is Nothing Then Exit Function

    ' 性能优化：一次性解除保护，批量更新，然后恢复保护
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If

    ' 加载基本信息
    Dim basicLoaded As Boolean
    basicLoaded = LoadHotelBasicInfoFast(ws, innCode)

    ' 加载USP信息
    Dim uspLoaded As Boolean
    uspLoaded = LoadHotelUSPInfoFast(ws, innCode)

    ' 恢复保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If

    LoadHotelInfoBatch = (basicLoaded Or uspLoaded)
    Exit Function

ErrorHandler:
    ' 确保恢复工作表保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If

    Call LogManager.LogEvent("ERROR", "批量加载酒店信息异常: " & Err.Description, HOTEL_INFO_SHEET, "加载异常")
    LoadHotelInfoBatch = False
End Function

' =============================================================================
' 双击事件处理函数
' =============================================================================

' 处理酒店信息工作表的双击事件
Public Sub HandleHotelInfoDoubleClick(ByVal Target As Range)
    On Error GoTo ErrorHandler

    ' 检查是否在酒店信息工作表
    If Target.Worksheet.Name <> HOTEL_INFO_SHEET Then Exit Sub

    ' 禁用屏幕更新以提升性能
    Dim originalScreenUpdating As Boolean
    originalScreenUpdating = Application.ScreenUpdating
    Application.ScreenUpdating = False

    Dim targetRow As Integer
    Dim targetCol As String
    Dim cellValue As String

    targetRow = Target.Row
    targetCol = Split(Target.Address, "$")(1)  ' 获取列字母
    cellValue = Trim(CStr(Target.Value))

    ' 处理源区域双击（33-37行）
    If targetRow >= 33 And targetRow <= 37 Then
        Select Case targetCol
            Case "C"
                Call AddToUSPColumn(Target.Worksheet, cellValue, USP_COL_ROOM)
            Case "D"
                Call AddToUSPColumn(Target.Worksheet, cellValue, USP_COL_FOOD)
            Case "E"
                Call AddToUSPColumn(Target.Worksheet, cellValue, USP_COL_MEETING)
            Case "F"
                Call AddToUSPColumn(Target.Worksheet, cellValue, USP_COL_ADDITIONAL)
            Case "H"
                Call AddToUSPColumn(Target.Worksheet, cellValue, USP_COL_OTHER)
        End Select

    ' 处理目标区域双击（25-29行）
    ElseIf targetRow >= USP_START_ROW And targetRow <= USP_END_ROW Then
        Select Case targetCol
            Case "C", "D", "E", "F", "H"
                Call ClearUSPCell(Target.Worksheet, targetRow, targetCol)
        End Select
    End If

    ' 恢复屏幕更新
    Application.ScreenUpdating = originalScreenUpdating
    Exit Sub

ErrorHandler:
    ' 恢复屏幕更新
    Application.ScreenUpdating = originalScreenUpdating
    Call LogManager.LogEvent("ERROR", "双击事件处理异常: " & Err.Description, HOTEL_INFO_SHEET, "事件处理")
End Sub

' 将内容添加到USP列的第一个空白单元格（带重复数据检查）
Private Sub AddToUSPColumn(ws As Worksheet, content As String, columnLetter As String)
    On Error GoTo ErrorHandler

    ' 如果内容为空，则不执行任何操作
    If Trim(content) = "" Then Exit Sub

    ' 性能优化：批量工作表保护管理
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If

    ' 首先检查是否存在重复数据
    Dim i As Integer
    Dim targetCell As String
    Dim cellValue As String
    Dim isDuplicate As Boolean
    isDuplicate = False

    For i = USP_START_ROW To USP_END_ROW
        targetCell = columnLetter & i
        cellValue = Trim(CStr(ws.Range(targetCell).Value))
        If cellValue <> "" And LCase(cellValue) = LCase(Trim(content)) Then
            isDuplicate = True
            Exit For
        End If
    Next i

    ' 如果发现重复数据，记录日志并退出
    If isDuplicate Then
        ' 恢复工作表保护
        If wasProtected Then
            ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                       AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        End If

        Call LogManager.LogEvent("WARNING", "重复数据，添加无效 - 列" & columnLetter & "已存在内容: " & content, HOTEL_INFO_SHEET, "双击操作")
        Exit Sub
    End If

    ' 查找第一个空白单元格进行添加
    Dim found As Boolean
    found = False

    For i = USP_START_ROW To USP_END_ROW
        targetCell = columnLetter & i
        If Trim(CStr(ws.Range(targetCell).Value)) = "" Then
            ws.Range(targetCell).Value = content
            found = True
            Exit For
        End If
    Next i

    ' 恢复工作表保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If

    ' 记录操作日志
    If found Then
        Call LogManager.LogEvent("INFO", "成功添加内容到" & targetCell & ": " & content, HOTEL_INFO_SHEET, "双击操作")
    Else
        Call LogManager.LogEvent("WARNING", "目标列" & columnLetter & "已满，无法添加内容: " & content, HOTEL_INFO_SHEET, "双击操作")
    End If

    Exit Sub

ErrorHandler:
    ' 确保恢复工作表保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If

    Call LogManager.LogEvent("ERROR", "添加USP内容异常: " & Err.Description, HOTEL_INFO_SHEET, "双击操作")
End Sub

' 清空USP单元格内容
Private Sub ClearUSPCell(ws As Worksheet, targetRow As Integer, columnLetter As String)
    On Error GoTo ErrorHandler

    ' 性能优化：批量工作表保护管理
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If

    Dim targetCell As String
    targetCell = columnLetter & targetRow

    ' 记录清空前的内容用于日志
    Dim originalContent As String
    originalContent = Trim(CStr(ws.Range(targetCell).Value))

    ' 清空单元格
    ws.Range(targetCell).Value = ""

    ' 恢复工作表保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If

    ' 记录操作日志
    If originalContent <> "" Then
        Call LogManager.LogEvent("INFO", "成功清空" & targetCell & "内容: " & originalContent, HOTEL_INFO_SHEET, "双击操作")
    End If

    Exit Sub

ErrorHandler:
    ' 确保恢复工作表保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If

    Call LogManager.LogEvent("ERROR", "清空USP单元格异常: " & Err.Description, HOTEL_INFO_SHEET, "双击操作")
End Sub

' =============================================================================
' 测试辅助函数
' =============================================================================

' 测试数据验证功能
Private Function TestDataValidation() As Boolean
    On Error GoTo ErrorHandler

    TestDataValidation = False

    ' 测试ValidateHotelInfoOptimized函数
    Dim result As Boolean
    result = ValidateHotelInfoOptimized()

    ' 如果验证函数能正常执行（无论结果如何），则认为功能正常
    TestDataValidation = True
    Exit Function

ErrorHandler:
    TestDataValidation = False
End Function

' 测试数据转换功能
Private Function TestDataConversion() As Boolean
    On Error GoTo ErrorHandler

    TestDataConversion = False

    ' 测试ConvertToDecimal函数
    Dim testValue1 As String
    Dim testValue2 As String
    Dim testValue3 As String

    testValue1 = ConvertToDecimal("1000")
    testValue2 = ConvertToDecimal("¥2,500.50")
    testValue3 = ConvertToDecimal("invalid")

    ' 检查转换结果是否符合预期
    If testValue1 = "1000" And testValue2 = "2500.50" And testValue3 = "0" Then
        TestDataConversion = True
    End If

    Exit Function

ErrorHandler:
    TestDataConversion = False
End Function


