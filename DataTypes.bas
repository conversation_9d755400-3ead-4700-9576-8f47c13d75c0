Option Explicit

' =============================================================================
' 数据类型定义模块 - 希尔顿酒店管理系统
' 定义各种数据结构和类型
' =============================================================================

' 酒店信息数据结构（完整版）
Public Type HotelInfoData
    ' 基本信息
    Id As String
    PlanOwner As String
    Inncode As String
    HotelName As String
    Region As String
    GMName As String
    ComDirector As String
    MktMgr As String
    MECCContact As String

    ' 预算信息
    BudgetLocal As String
    BudgetCoop As String
    BudgetPMP As String
    BudgetTotal As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' 酒店USP数据结构（单行）
Public Type HotelUSPRowData
    Id As String
    InnCode As String
    RowIndex As Integer
    Room As String
    Food As String
    Meeting As String
    Additional As String
    OtherService As String
End Type

' 酒店USP数据集合（5行）
Public Type HotelUSPData
    Rows(1 To 5) As HotelUSPRowData
    InnCode As String
    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' 预算数据结构
Public Type BudgetData
    ' 原始数据
    LocalBudgetRaw As String
    CorporateFundRaw As String
    PMPRaw As String
    TotalBudgetRaw As String

    ' 转换后的数据库格式
    LocalBudget As String
    CorporateFund As String
    PMP As String
    TotalBudget As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' USP数据结构
Public Type USPData
    ' 表格范围信息
    StartRow As Integer
    EndRow As Integer
    MaxCol As Integer

    ' USP类别数据
    GuestRooms As String
    FoodBeverage As String
    Meetings As String
    AdditionalServices As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' 业务背景预测数据结构
Public Type BusinessForecastData
    ' 类别信息 (OCC, ADR, RevPAR)
    Category As String

    ' 月度数据 (1-12月)
    Month1 As String
    Month2 As String
    Month3 As String
    Month4 As String
    Month5 As String
    Month6 As String
    Month7 As String
    Month8 As String
    Month9 As String
    Month10 As String
    Month11 As String
    Month12 As String

    ' 全年OCC
    WholeYearOCC As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' 市场细分数据结构
Public Type MarketSegmentData
    ' 市场细分数据 (B10-B23)
    BAR As String
    CONS As String
    DISC As String
    MKT As String
    IT As String
    LNR As String
    CNR As String
    GOV As String
    CMTG As String
    CONV As String
    GT As String
    SMRF As String
    PERM As String
    Total As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' 渠道组合数据结构
Public Type ChannelMixData
    ' 渠道组合数据 (N11-N15)
    PropertyDirect As String
    OTA As String
    Fliggy As String
    DD As String
    GDS As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' GOB数据结构
Public Type GOBData
    ' GOB数据 (B27-B36)
    China As String
    USA As String
    HongKong As String
    Germany As String
    UnitedKingdom As String
    SouthKorea As String
    Singapore As String
    France As String
    Malaysia As String
    India As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' 年度重点活动数据结构
Public Type AnnualKeyEventsData
    ' 年度重点活动数据 (M20:P24 - 5行数据)
    Event1_Name As String
    Event1_Time As String
    Event1_MarketSegment As String
    Event1_Frequency As String

    Event2_Name As String
    Event2_Time As String
    Event2_MarketSegment As String
    Event2_Frequency As String

    Event3_Name As String
    Event3_Time As String
    Event3_MarketSegment As String
    Event3_Frequency As String

    Event4_Name As String
    Event4_Time As String
    Event4_MarketSegment As String
    Event4_Frequency As String

    Event5_Name As String
    Event5_Time As String
    Event5_MarketSegment As String
    Event5_Frequency As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' 市场信息数据结构
Public Type MarketInfoData
    ' 市场信息数据 (M27:P37 - 11行数据)
    Row27_BusinessSegment As String
    Row27_SegmentSelection As String
    Row27_MarketingObjectives As String
    Row27_TargetAudience As String

    Row28_BusinessSegment As String
    Row28_SegmentSelection As String
    Row28_MarketingObjectives As String
    Row28_TargetAudience As String

    Row29_BusinessSegment As String
    Row29_SegmentSelection As String
    Row29_MarketingObjectives As String
    Row29_TargetAudience As String

    Row30_BusinessSegment As String
    Row30_SegmentSelection As String
    Row30_MarketingObjectives As String
    Row30_TargetAudience As String

    Row31_BusinessSegment As String
    Row31_SegmentSelection As String
    Row31_MarketingObjectives As String
    Row31_TargetAudience As String

    Row32_BusinessSegment As String
    Row32_SegmentSelection As String
    Row32_MarketingObjectives As String
    Row32_TargetAudience As String

    Row33_BusinessSegment As String
    Row33_SegmentSelection As String
    Row33_MarketingObjectives As String
    Row33_TargetAudience As String

    Row34_BusinessSegment As String
    Row34_SegmentSelection As String
    Row34_MarketingObjectives As String
    Row34_TargetAudience As String

    Row35_BusinessSegment As String
    Row35_SegmentSelection As String
    Row35_MarketingObjectives As String
    Row35_TargetAudience As String

    Row36_BusinessSegment As String
    Row36_SegmentSelection As String
    Row36_MarketingObjectives As String
    Row36_TargetAudience As String

    Row37_BusinessSegment As String
    Row37_SegmentSelection As String
    Row37_MarketingObjectives As String
    Row37_TargetAudience As String

    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' 基础运营检查表单行数据结构
Public Type BasicOperationsRowData
    RowNumber As Integer
    BusinessFocusSegment As String      ' Column A - 生意聚焦板块
    ChannelProject As String            ' Column B - 渠道/项目
    ChannelContent As String            ' Column C - 渠道内容
    DetailedDescription As String       ' Column D - 详细描述
    BusinessSegment As String           ' Column E - 生意板块
    TargetMarket As String              ' Column F - 目标市场
    Purpose As String                   ' Column G - 目的
    ExecutionPlanSummary As String      ' Column H - 执行计划简述
    KPI As String                       ' Column I - KPI
    ExecutionProgress As String         ' Column J - 执行进度
    Remark As String                    ' Column K - 备注
End Type

' 基础运营检查表数据结构 (动态行数)
Public Type BasicOperationsData
    ' 动态数组存储所有行数据
    Rows() As BasicOperationsRowData
    RowCount As Integer
    StartRow As Integer                 ' 起始行号 (通常为3)
    EndRow As Integer                   ' 结束行号
    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type

' USP单元格地址信息结构
Public Type USPCellInfo
    Category As String
    CellAddress As String
    Value As String
End Type

' 同步状态枚举
Public Enum SyncStatus
    SyncSuccess = 0
    SyncFailed = 1
    SyncWarning = 2
    SyncSkipped = 3
End Enum

' 数据验证结果
Public Type ValidationResult
    IsValid As Boolean
    ErrorCount As Integer
    WarningCount As Integer
    Messages As String
End Type

' 错误信息结构
Public Type ErrorInfo
    ErrorNumber As Long
    ErrorDescription As String
    ErrorSource As String
    ModuleName As String
    FunctionName As String
    Timestamp As Date
End Type

' 同步配置
Public Type SyncConfig
    EnableLogging As Boolean
    EnableValidation As Boolean
    EnableErrorAnalysis As Boolean
    MaxRetryCount As Integer
    TimeoutSeconds As Integer
End Type

' 表格检测结果
Public Type TableDetectionResult
    Found As Boolean
    StartRow As Integer
    EndRow As Integer
    StartCol As Integer
    EndCol As Integer
    RowCount As Integer
    ColCount As Integer
End Type

' 字典数据项结构
Public Type DictionaryItem
    Id As String
    DataType As String
    Content As String
    Parent As String
    ModifyTime As String
End Type

' 段选择器数据结构
Public Type SegmentPickerData
    BusinessFocusSegment As String      ' 生意聚焦板块
    ChannelProject As String            ' 渠道/项目
    TargetRow As Integer                ' 目标行号
    TargetWorksheet As String           ' 目标工作表名称
End Type

' 市场计划选择器数据结构
Public Type MarketPlanPickerData
    BusinessFocusSection As String      ' 生意聚焦板块
    Channel As String                   ' 渠道
    ChannelStrategy As String           ' 渠道策略
    Details As String                   ' 详细信息
    TargetRow As Integer                ' 目标行号
    TargetWorksheet As String           ' 目标工作表名称
End Type

' 市场计划字典项结构
Public Type MarketPlanDictionaryItem
    Id As String
    Channel As String
    ChannelStrategy As String
    Details As String
    ModifyTime As String
End Type

' 市场细分选择器数据结构
Public Type MarketPlanSegmentPickerData
    RoomSelected As Boolean             ' 房间选择状态
    FBSelected As Boolean               ' 餐饮选择状态
    GMESelected As Boolean              ' 会议及会务选择状态
    OtherSelected As Boolean            ' 其他选择状态
    SelectedSegments As String          ' 选择的细分项目（逗号分隔）
    TargetRow As Integer                ' 目标行号
    TargetWorksheet As String           ' 目标工作表名称
End Type

' 市场选择器数据结构
Public Type MarketPickerData
    InSelected As Boolean               ' 国内选择状态
    OutSelected As Boolean              ' 国外选择状态
    SelectedMarkets As String           ' 选择的市场（逗号分隔）
    TargetRow As Integer                ' 目标行号
    TargetWorksheet As String           ' 目标工作表名称
End Type

' 目标选择器数据结构
Public Type GoalPickerData
    SalesConversionSelected As Boolean  ' 销售转化选择状态
    ConsumerDecisionSelected As Boolean ' 消费决策考量选择状态
    HotelVisibilitySelected As Boolean  ' 酒店声量选择状态
    SelectedGoals As String             ' 选择的目标（逗号分隔）
    TargetRow As Integer                ' 目标行号
    TargetWorksheet As String           ' 目标工作表名称
End Type

' 市场计划单行数据结构
Public Type MarketingPlanRowData
    RowNumber As Integer                ' Excel行号
    PrioritySegment As String           ' A列 - 生意聚焦板块
    Channel As String                   ' B列 - 渠道类型/项目
    ChannelStrategy As String           ' C列 - 渠道内容
    Details As String                   ' D列 - 详细描述
    BusinessSegment As String           ' E列 - 生意板块
    TargetMarket As String              ' F列 - 目标市场
    Purpose As String                   ' G列 - 目的
    ProjectManager As String            ' H列 - 项目管理方
    ExecutionPlan As String             ' I列 - 执行计划简述
    Month1 As String                    ' J列 - 1月
    Month2 As String                    ' K列 - 2月
    Month3 As String                    ' L列 - 3月
    Month4 As String                    ' M列 - 4月
    Month5 As String                    ' N列 - 5月
    Month6 As String                    ' O列 - 6月
    Month7 As String                    ' P列 - 7月
    Month8 As String                    ' Q列 - 8月
    Month9 As String                    ' R列 - 9月
    Month10 As String                   ' S列 - 10月
    Month11 As String                   ' T列 - 11月
    Month12 As String                   ' U列 - 12月
    TotalBudget As String               ' V列 - 总预算
    KPI As String                       ' W列 - KPI
    TargetROI As String                 ' X列 - Target ROI
    ExecutionProgress As String         ' Y列 - 执行进度
    ActualRevenue As String             ' Z列 - 实际完成revenue
    ROI As String                       ' AA列 - ROI
    Remark As String                    ' AB列 - remark
End Type

' 市场计划数据结构 (动态行数)
Public Type MarketingPlanData
    ' 动态数组存储所有行数据
    Rows() As MarketingPlanRowData
    RowCount As Integer
    StartRow As Integer                 ' 起始行号 (通常为5)
    EndRow As Integer                   ' 结束行号
    Version As String                   ' 版本信息 (Budget/Actual/Both)
    ' 单元格地址信息（JSON格式）
    CellAddresses As String
End Type
