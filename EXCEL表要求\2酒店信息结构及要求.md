# 一、酒店信息

1. sheet名称： “1.酒店信息”

2. 单元格区域即数据库对应:

   -  区域一：对应数据库表 hotel_info

     - 单元格地址与数据库字段对应

       | 数据库字段        | 单元格地址 | 中文说明                        |
       | ------------ | ----- | --------------------------- |
       | id           | A5    | 记录唯一ID（主键），如果id单元格为空则自动生成写入 |
       | inn_code     | D6    | 酒店Inn Code（数据权限控制）          |
       | plan_owner   | D5    | 计划制定人                       |
       | hotel_name   | D7    | 酒店名称                        |
       | region       | D8    | 酒店所属区域                      |
       | gm_name      | D9    | 总经理                         |
       | com_director | D10   | 商务总监                        |
       | mkt_mgr      | D11   | 市场总监/经理                     |
       | mecc_contact | D12   | MECC联系人                     |
       | budget_local | D16   | 酒店本地活动预算                    |
       | budget_coop  | D17   | 集团市场统筹费（Co-op Fund）         |
       | budget_pmp   | D18   | PMP预算                       |
       | budget_total | D19   | 总预算                         |
       | created_at   | N/A   | 创建时间                        |
       | updated_at   | N/A   | 最后更新时间                      |

   - 区域二： 对应数据库表 hotel_usp,读取“1.酒店信息”sheet的固定5行

     | 数据库字段         | 单元格地址     | 中文说明              |
     | ------------- | --------- | ----------------- |
     | id            | A25-A29   | 如果id单元格为空则自动生成写入  |
     | inn_code      | 从Token中获取 | 酒店 Inn Code（权限控制） |
     | room          | C25-C29   | 客房相关 USP 描述       |
     | food          | D25-D29   | 餐饮相关 USP 描述       |
     | meeting       | E25-E29   | 会议及宴会 USP 描述      |
     | other_service | H25-H29   | 其他服务 USP 描述       |
     | created_at    | 系统生成      | 创建时间              |
     | updated_at    | 系统生成      | 最后修改时间            |

3. 数据存储要求： 
   1. 用户保存时，自动将区域一、区域二的内容保存至数据库
   2. 区域二不用判断是否为空白，无论是否为空白，都要将固定5行存入数据库表中
   3. ID单元格为空时，意味着首次写入，要自动生成
   4. 用户打开excel时，自动将数据从数据库表读取并写入到指定单元格（根据用户Token中的inncode筛选数据）

4. 其他要求：

   1. 在“日志”表和 数据库表logs (这两个我确定已经存在)进行必要、简洁的日志输出。 logs表结构详见“SQL.md”
   2. 关键点写入中文注释
   3. 方法函数前写明中文函数功能注释

5.数据检查：
   如果下面数据为空，在所在单元格中添加醒目标记，弹窗提示无法保存。注意，保存为空时的单元格样式，填写后从新变回正常样式。注意，工作表设定了保护
   1. inn_code不能为空，否则不允许保存
   2. plan_owner不能为空，否则不允许保存
   3. hotel_name不能为空，否则不允许保存
   4. region不能为空，否则不允许保存
   5. gm_name不能为空，否则不允许保存
   6. com_director不能为空，否则不允许保存
   7. mkt_mgr不能为空，否则不允许保存
   8. mecc_contact不能为空，否则不允许保存