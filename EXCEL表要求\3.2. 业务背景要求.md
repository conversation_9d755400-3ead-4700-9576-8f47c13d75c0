# 三、业务背景
## 总体叙述
    - 1.这个sheet的名称叫做"2. 业务背景" 
    - 2.这个sheet分为7个区域

## 一.区域一 :酒店生意预测

    - 1.ui如下图
![alt text](image.png)
```
- 2.具体说明
    1) O4单元格是下拉列表， 选项为：2025年预算 RF1 RF2 RF3 .... RF12。代表13个版本， 其中2025年预算代表全年预算，只能提交一次，后面不能在修改，只读。 RF1-RF12代表每个月可填写的预算版本，可编辑。
    2）B6\B7\B8的值为OCC、ADR、RevPAR，为固定值，是分类。
    3）C5-N5是表头，值为1月...12月。
    3）需要存储到数据库的区域为C6至N8（固定）， 其中C列对应1月....N列对应12月
    4）请注意，切换O4单元格代表不同版本的切换，要清除上一个版本的值，从数据库读取该版本的值
    5）当版本为“2025年预算”时， 需要弹窗警告用户只能保存一次，并创建独立方法检查C6至N8区域，将未填写的区域进行标注（参考酒店信息中标注方法），在用户填写后恢复格式。如果用户有未填写的区域，但是无视警告仍然提交时，允许提交
```
​      

```
- 3. 要求
	1.给我具体的方案设计和SQL，然后实现vba代码
	2.不要使用emo， excel不支持显示
	3.只保留一个综合测试方法
	4.不要编写说明文档、功能文档、测试文档
```

## 二、区域二：酒店生意细分占比区域

1. ui如下图

2. 具体说明

  - B12-B24单元格为表头区域，其内容为：

  - C12-C24为数据存储区域(固定)、A12-A24为id区域，如果没有值自动创建，进行数据存储与更新
  - C12-C24中的值相加应等于100%， 如果不等于1，那么在D10单元格进行警告，在用户修改后取消警告

  ![alt text](image-1.png)

##  三、区域三：酒店渠道占比


1. ui如下图

2. 具体说明

   - N13-N17为表头区域，值为：Property Direct 

    - O13-O17为需存储的数值区域，O13-O17中的值相加应等于100%， 如果不等于1，那么在N10单元格进行警告，在用户修改后取消警告

 3. 要求：


      1. 给我具体的方案设计和SQL，然后实现vba代码

      2. 不要使用emo， excel不支持显示

      3. 只保留一个综合测试方法

          ​

## 四、区域四：酒店GOB分析

1. ui如下图

2. 具体说明

   - B29-B38为表头区域，值为：China

   - C29-C38为需存储的数值区域，C29-C38中的值相加应等于100%， 如果不等于1，那么在B40单元格进行警告，在用户修改后取消警告

3. 要求：

   1. 给我具体的方案设计和SQL，然后实现vba代码
   2. 不要使用emo， excel不支持显示
   3. 只保留一个综合测试方法
   4. 明确出2个主入口方法，保存和从数据库恢复
   5. 不要创建测试方法、说明文档、方案等，我会手动测试
   6. sql中除了要有row_index,也应该有column_index
   7. 模仿酒店渠道占比

## 五、个人旅游价区域
![alt text](image-2.png)
1.如图，此区域是一个动态表格，初始区域为42-44行，B列-L列。B41-L41是标题列， 先用ColB-ColL代替进行数据库表设计。A列是隐藏列，用于辅助进行程序计算，初始A42-A44的值固定位“个人旅游价区域”，用来定位
2.其中，B42-B44为合并单元格，，C42-C44为合并单元格
3.C45单元格有一个 add more按钮，负责动态添加行数
4.要实现这个动态区域的数据库存储与恢复，包括合并单元格
5.实现方案(参考)：
  a.add more如果被点击，第一步先在A列写入“个人旅游价区域”，用来帮助进行表格区域定位
  b.向数据库存储前，首先判断从42行开始有多少行值为“个人旅游价区域”，假设最后一行为44，那么行区域就是42-44，然后用数组读取B42-L44，因为数组读取会自动拆分并填充合并单元格到元素，然后存入数据库。
  b.从数据库恢复前，先清空42行至最后一行A列是“个人旅游价区域”行的值，然后根据数据库中读取的行数，动态添加行，然后将数据库中的值写入到对应单元格
  c.数据库表设计参考酒店渠道占比
  d.写入和读取数据库的主入口方法要写到BusinessForecastManager.bas中
  e.注意，工作表设定了保护，要解除保护后操作，操作完要恢复保护


## 六、年度重大事件区域

1.如图，此区域仍然是一个动态表格，但是没有合并单元格

2.表格区域从49行开始，49行B\C\D\E列是表头，值为

| 活动/展会名称 | 时间   | 影响到的市场板块 | 频率   |
| ------- | ---- | -------- | ---- |
|         |      |          |      |

3.数据区域从50行开始，B至E列需要进行储存

4.用户点击addmore按钮需要动态添加一行到表格，addmore按钮名称为Add_More2,初始在51行

5.需要实现sql，2个主入口方法，保存和从数据库恢复