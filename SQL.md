## 表一：hotel_info – 酒店信息主表
```
DROP TABLE IF EXISTS `hotel_info`;

CREATE TABLE hotel_info (
    id VARCHAR(50) PRIMARY KEY COMMENT '记录唯一ID',
    inn_code VARCHAR(50) NOT NULL COMMENT '酒店Inn Code，用于数据权限验证',

    plan_owner VARCHAR(100) COMMENT '计划制定人',
    hotel_name VARCHAR(200) COMMENT '酒店名称',
    region VARCHAR(100) COMMENT '酒店所属区域',
    gm_name VARCHAR(100) COMMENT '总经理',
    com_director VARCHAR(100) COMMENT '商务总监',
    mkt_mgr VARCHAR(100) COMMENT '市场总监/经理',
    mecc_contact VARCHAR(100) COMMENT 'MECC联系人',

    budget_local DECIMAL(15,2) COMMENT '酒店本地活动预算',
    budget_coop DECIMAL(15,2) COMMENT '集团市场统筹费（Co-op Fund）',
    budget_pmp DECIMAL(15,2) COMMENT 'PMP预算',
    budget_total DECIMAL(15,2) COMMENT '总预算',

    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店信息主表';
```

## 表二：hotel_usp – 酒店USP表（酒店卖点）
```
DROP TABLE IF EXISTS `hotel_usp`;

CREATE TABLE hotel_usp (
    id VARCHAR(050) PRIMARY KEY COMMENT '主键唯一标识，建议为UUID',
    inn_code VARCHAR(20) NOT NULL COMMENT '酒店Inn Code，用于数据权限控制',
    row_index INT NOT NULL COMMENT '第几行记录（用于排序）',
    room TEXT COMMENT '客房相关USP描述',
    food TEXT COMMENT '餐饮相关USP描述',
    meeting TEXT COMMENT '会议及宴会相关USP描述',
    other_service TEXT COMMENT '其他服务相关USP描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间'
) COMMENT='酒店USP表，每行表示一个USP组合记录';


```

## 表四：business_forecast – 酒店生意预测表
```
DROP TABLE IF EXISTS `business_forecast`;

CREATE TABLE business_forecast (
    id VARCHAR(50) PRIMARY KEY COMMENT '记录唯一ID',
    inn_code VARCHAR(50) NOT NULL COMMENT '酒店Inn Code，用于数据权限验证',
    version VARCHAR(20) NOT NULL COMMENT '预算版本：2025年预算, RF1, RF2, ..., RF12',
    category VARCHAR(20) NOT NULL COMMENT '预测类别：OCC, ADR, RevPAR',

    month_1 DECIMAL(10,2) COMMENT '1月数据',
    month_2 DECIMAL(10,2) COMMENT '2月数据',
    month_3 DECIMAL(10,2) COMMENT '3月数据',
    month_4 DECIMAL(10,2) COMMENT '4月数据',
    month_5 DECIMAL(10,2) COMMENT '5月数据',
    month_6 DECIMAL(10,2) COMMENT '6月数据',
    month_7 DECIMAL(10,2) COMMENT '7月数据',
    month_8 DECIMAL(10,2) COMMENT '8月数据',
    month_9 DECIMAL(10,2) COMMENT '9月数据',
    month_10 DECIMAL(10,2) COMMENT '10月数据',
    month_11 DECIMAL(10,2) COMMENT '11月数据',
    month_12 DECIMAL(10,2) COMMENT '12月数据',

    is_submitted TINYINT(1) DEFAULT 0 COMMENT '是否已提交（仅对2025年预算有效）',

    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',

    UNIQUE KEY unique_forecast (inn_code, version, category) COMMENT '确保每个酒店每个版本每个类别只有一条记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店生意预测表';
```

## 表三：hilton_users – 用户信息表
```
DROP TABLE IF EXISTS `hilton_users`;

CREATE TABLE `hilton_users`  (
  `Id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `Username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `Password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `HotelName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联酒店名称',
  `HotelInncode` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '酒店Inn Code',
  `IsActive` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `LastLoginTime` timestamp(0) DEFAULT NULL COMMENT '最后登录时间',
  `CreateTime` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `ModifyTime` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `CellAddresses` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '储存单元格地址',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `unique_username`(`Username`) USING BTREE,
  INDEX `idx_username_password`(`Username`, `Password`) USING BTREE,
  INDEX `idx_hotel_name`(`HotelName`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hilton_users
-- ----------------------------
INSERT INTO `hilton_users` VALUES ('1', 'test', '123', 'avahs', 'avahs', 1, '2025-06-10 06:29:33', '2025-06-10 06:28:50', '2025-06-10 06:29:33', NULL);

SET FOREIGN_KEY_CHECKS = 1;
```


### business_segment_ratio 表结构
```sql
CREATE TABLE business_segment_ratio (
    id VARCHAR(50) PRIMARY KEY COMMENT '记录唯一ID',
    inn_code VARCHAR(50) NOT NULL COMMENT '酒店Inn Code',
    row_index INT NOT NULL COMMENT '行索引(12-24)',
    ratio_value DECIMAL(5,4) NOT NULL COMMENT '占比值(0-1)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_segment (inn_code, row_index) COMMENT '确保每个酒店每行只有一条记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店生意细分占比表';
```

### 酒店渠道占比表结构 hotel_channel_ratio
```
CREATE TABLE hotel_channel_ratio (
    id VARCHAR(50) PRIMARY KEY COMMENT '记录唯一ID',
    inn_code VARCHAR(50) NOT NULL COMMENT '酒店Inn Code',
    row_index INT NOT NULL COMMENT '行索引(13-17)',
    ratio_value DECIMAL(5,4) NOT NULL COMMENT '占比值(0-1)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_channel (inn_code, row_index) COMMENT '确保每个酒店每行只有一条记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='酒店渠道占比表';
```

### 个人旅游价区域动态表格 personal_travel_price_region
```sql
CREATE TABLE personal_travel_price_region (
    id VARCHAR(50) PRIMARY KEY COMMENT '记录唯一ID',
    inn_code VARCHAR(50) NOT NULL COMMENT '酒店Inn Code',
    row_index INT NOT NULL COMMENT '实际Excel行号(42开始)',
    column_index INT NOT NULL COMMENT '列索引(2-12对应B-L)',
    value TEXT COMMENT '单元格值',
    table_type VARCHAR(50) DEFAULT 'personal_travel_price' COMMENT '表格类型标识',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_cell (inn_code, row_index, column_index, table_type) COMMENT '确保每个酒店每个单元格只有一条记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人旅游价区域动态表格';
```

### Hotel GOB Analysis表结构 hotel_gob_analysis
```sql
CREATE TABLE hotel_gob_analysis (
    id VARCHAR(50) PRIMARY KEY COMMENT '记录唯一ID',
    inn_code VARCHAR(50) NOT NULL COMMENT '酒店Inn Code',
    row_index INT NOT NULL COMMENT '行索引(29-38)',
    ratio_value DECIMAL(5,4) NOT NULL COMMENT '占比值(0-1)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_gob (inn_code, row_index) COMMENT '确保每个酒店每行只有一条记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Hotel GOB Analysis表';
```