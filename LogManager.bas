Option Explicit

' =============================================================================
' 日志管理模块 - 希尔顿酒店管理系统
' 提供统一的日志记录、管理和查看功能
' 使用logs表结构，支持inn_code权限控制和sheet_name记录
' =============================================================================

' 日志记录配置（精简版 - 仅Excel日志）
Public Const LOG_TO_EXCEL As Boolean = True        ' 是否记录到Excel
Public Const CLEAR_EXCEL_ON_NEW_OPERATION As Boolean = True  ' 新操作时是否清空Excel日志

' 标记是否已经清空过Excel日志（避免重复清空）
Private g_ExcelLogsCleared As Boolean

' =============================================================================
' 核心日志记录函数
' =============================================================================

' 记录一般日志（主要接口函数）
Public Sub LogEvent(eventType As String, message As String, Optional sheetName As String = "", Optional remark As String = "")
    On Error Resume Next

    ' 如果没有指定sheet名称，使用当前活动sheet
    If Trim(sheetName) = "" Then
        sheetName = GetCurrentSheetName()
    End If

    ' 记录到数据库和Excel
    Call WriteToLogSystems(eventType, message, sheetName, remark)
End Sub

' 记录同步事件日志（兼容旧接口）
Public Sub LogSyncEvent(eventType As String, message As String, Optional operationType As String = "SYNC", Optional moduleName As String = "", Optional recordID As String = "")
    On Error Resume Next

    ' 使用新的接口，将operationType作为remark
    Call LogEvent(eventType, message, moduleName, operationType)
End Sub

' 安全事件日志记录函数
Public Sub LogSecurityEvent(eventType As String, message As String, Optional sheetName As String = "")
    On Error Resume Next

    ' 如果没有指定工作表名称，使用"系统安全"作为默认值
    If sheetName = "" Then
        sheetName = "系统安全"
    End If

    ' 调用主要日志记录函数，使用"SECURITY"作为备注
    Call LogEvent(eventType, message, sheetName, "SECURITY")
End Sub

' 开始新的操作会话（清空Excel日志）
Public Sub StartNewOperationSession(operationType As String, Optional sheetName As String = "")
    On Error Resume Next

    ' 重置Excel日志清空标志，强制下次记录时清空
    g_ExcelLogsCleared = False

    ' 记录操作开始（这会触发Excel日志清空）
    Call LogEvent("INFO", "开始新操作: " & operationType, sheetName, operationType)
End Sub

' 结束当前操作会话
Public Sub EndCurrentOperationSession(operationType As String, Optional status As String = "SUCCESS", Optional sheetName As String = "")
    On Error Resume Next

    ' 记录操作结束
    Call LogEvent("INFO", "操作完成: " & operationType & " [" & status & "]", sheetName, operationType)
End Sub

' 手动清空Excel日志
Public Sub ManualClearExcelLogs()
    On Error Resume Next
    Call ClearExcelLogs
    g_ExcelLogsCleared = True
    Call LogEvent("INFO", "手动清空Excel日志", "系统", "MAINTENANCE")
End Sub

' =============================================================================
' 统一日志记录系统
' =============================================================================

' 写入日志到Excel工作表（精简版）
Private Sub WriteToLogSystems(eventType As String, message As String, sheetName As String, remark As String)
    On Error Resume Next

    ' 仅记录到Excel工作表
    If LOG_TO_EXCEL Then
        ' 如果配置启用且还没有清空过Excel日志，则先清空
        If CLEAR_EXCEL_ON_NEW_OPERATION And Not g_ExcelLogsCleared Then
            Call ClearExcelLogs
            g_ExcelLogsCleared = True
        End If

        Call WriteToLogSheet(eventType, message)
    End If
End Sub



' =============================================================================
' Excel工作表日志记录
' =============================================================================

' =============================================================================
' Excel工作表日志记录
' =============================================================================

' 写入日志到Excel工作表
Private Sub WriteToLogSheet(eventType As String, message As String)
    On Error Resume Next

    Dim logWs As Worksheet
    Dim lastRow As Long
    Dim currentTime As String

    ' 获取或创建日志工作表
    Set logWs = GetOrCreateLogSheet()
    If logWs Is Nothing Then Exit Sub

    ' 格式化时间
    currentTime = Format(Now, "yyyy-mm-dd hh:nn:ss")

    ' 查找最后一行
    lastRow = logWs.Cells(logWs.Rows.Count, 1).End(xlUp).Row
    If lastRow = 1 And logWs.Cells(1, 1).Value = "" Then
        ' 如果是空工作表，添加标题行
        Call InitializeLogSheet(logWs)
        lastRow = 1
    End If

    ' 写入新的日志记录
    lastRow = lastRow + 1
    logWs.Cells(lastRow, 1).Value = currentTime
    logWs.Cells(lastRow, 2).Value = eventType
    logWs.Cells(lastRow, 3).Value = message
    logWs.Cells(lastRow, 4).Value = TokenManager.GetCurrentUser()

    ' 根据事件类型设置颜色
    Call SetLogRowColor(logWs, lastRow, eventType)

    ' 自动调整列宽（仅前几次）
    If lastRow <= 10 Then
        logWs.Columns("A:D").AutoFit
    End If
End Sub

' 获取或创建日志工作表
Private Function GetOrCreateLogSheet() As Worksheet
    On Error GoTo CreateSheet

    ' 尝试获取现有的日志工作表
    Set GetOrCreateLogSheet = ThisWorkbook.Worksheets("日志")
    Exit Function

CreateSheet:
    On Error GoTo ErrorHandler

    ' 创建新的日志工作表
    Set GetOrCreateLogSheet = ThisWorkbook.Worksheets.Add
    GetOrCreateLogSheet.Name = "日志"

    ' 初始化工作表
    Call InitializeLogSheet(GetOrCreateLogSheet)
    Exit Function

ErrorHandler:
    Set GetOrCreateLogSheet = Nothing
End Function

' 初始化日志工作表
Private Sub InitializeLogSheet(ws As Worksheet)
    On Error Resume Next

    ' 设置标题行
    ws.Cells(1, 1).Value = "时间"
    ws.Cells(1, 2).Value = "类型"
    ws.Cells(1, 3).Value = "消息"
    ws.Cells(1, 4).Value = "用户"

    ' 设置标题行格式
    With ws.Range("A1:D1")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .Borders.LineStyle = xlContinuous
    End With

    ' 设置列宽
    ws.Columns("A").ColumnWidth = 20  ' 时间列
    ws.Columns("B").ColumnWidth = 10  ' 类型列
    ws.Columns("C").ColumnWidth = 50  ' 消息列
    ws.Columns("D").ColumnWidth = 15  ' 用户列
End Sub

' 根据事件类型设置行颜色
Private Sub SetLogRowColor(ws As Worksheet, row As Long, eventType As String)
    On Error Resume Next

    Dim colorValue As Long

    Select Case UCase(eventType)
        Case "ERROR"
            colorValue = RGB(255, 200, 200)  ' 浅红色
        Case "WARNING"
            colorValue = RGB(255, 255, 200)  ' 浅黄色
        Case "INFO"
            colorValue = RGB(200, 255, 200)  ' 浅绿色
        Case Else
            colorValue = RGB(240, 240, 240)  ' 浅灰色
    End Select

    ws.Range("A" & row & ":D" & row).Interior.Color = colorValue
End Sub

' 清空Excel日志（保留标题行）
Private Sub ClearExcelLogs()
    On Error Resume Next

    Dim logWs As Worksheet
    Dim lastRow As Long

    Set logWs = GetOrCreateLogSheet()
    If logWs Is Nothing Then Exit Sub

    ' 查找最后一行
    lastRow = logWs.Cells(logWs.Rows.Count, 1).End(xlUp).Row

    ' 如果有数据行（除了标题行），则删除
    If lastRow > 1 Then
        logWs.Range("A2:D" & lastRow).ClearContents
        logWs.Range("A2:D" & lastRow).Interior.ColorIndex = xlNone
    End If
End Sub



' =============================================================================
' 数据库日志管理功能
' =============================================================================

' 查看Excel日志统计（替代数据库日志统计）
Public Sub ShowExcelLogStatistics()
    On Error GoTo ErrorHandler

    Dim logWs As Worksheet
    Set logWs = GetOrCreateLogSheet()

    If logWs Is Nothing Then
        MsgBox "无法访问日志工作表", vbExclamation, "统计错误"
        Exit Sub
    End If

    Dim lastRow As Long
    Dim totalCount As Long
    Dim errorCount As Long
    Dim warningCount As Long
    Dim infoCount As Long
    Dim i As Long
    Dim eventType As String
    Dim msg As String

    ' 获取数据行数
    lastRow = logWs.Cells(logWs.Rows.Count, 1).End(xlUp).Row
    totalCount = IIf(lastRow > 1, lastRow - 1, 0)  ' 减去标题行

    ' 统计各类型日志
    For i = 2 To lastRow
        eventType = UCase(Trim(CStr(logWs.Cells(i, 2).Value)))
        Select Case eventType
            Case "ERROR"
                errorCount = errorCount + 1
            Case "WARNING"
                warningCount = warningCount + 1
            Case "INFO"
                infoCount = infoCount + 1
        End Select
    Next i

    ' 构建统计信息
    msg = "Excel日志统计" & vbCrLf & vbCrLf
    msg = msg & "总记录数: " & totalCount & vbCrLf & vbCrLf
    msg = msg & "按类型统计:" & vbCrLf
    msg = msg & "• 信息 (INFO): " & infoCount & vbCrLf
    msg = msg & "• 警告 (WARNING): " & warningCount & vbCrLf
    msg = msg & "• 错误 (ERROR): " & errorCount & vbCrLf & vbCrLf

    If totalCount > 0 Then
        msg = msg & "最新记录时间: " & logWs.Cells(lastRow, 1).Value
    End If

    MsgBox msg, vbInformation, "Excel日志统计"
    Exit Sub

ErrorHandler:
    MsgBox "获取Excel日志统计失败: " & Err.Description, vbCritical, "统计错误"
End Sub

' =============================================================================
' 日志管理功能
' =============================================================================

' 导航到日志工作表
Public Sub GoToLogSheet()
    On Error Resume Next

    Dim logWs As Worksheet
    Set logWs = GetOrCreateLogSheet()

    If Not logWs Is Nothing Then
        logWs.Activate
        logWs.Range("A1").Select
        Call LogEvent("INFO", "用户查看日志工作表", "系统", "VIEW")
    Else
        MsgBox "无法访问日志工作表", vbExclamation
    End If
End Sub

' =============================================================================
' 辅助函数
' =============================================================================

' 获取当前活动的Sheet名称
Private Function GetCurrentSheetName() As String
    On Error Resume Next
    GetCurrentSheetName = "系统"  ' 默认值

    ' 尝试获取当前活动的工作表名称
    If Not ActiveSheet Is Nothing Then
        GetCurrentSheetName = ActiveSheet.Name
    End If

    If Err.Number <> 0 Then
        GetCurrentSheetName = "系统"
        Err.Clear
    End If

    ' 确保返回值不为空
    If Trim(GetCurrentSheetName) = "" Then
        GetCurrentSheetName = "系统"
    End If
End Function

