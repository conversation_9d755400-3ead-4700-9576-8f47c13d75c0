Option Explicit

' =============================================================================
' 工作表可见性管理模块 - 希尔顿酒店管理系统
' 管理Excel工作表的显示和隐藏，实现基于登录状态的访问控制
' =============================================================================

' 工作表可见性状态常量
Private Const SHEET_VISIBLE As Integer = -1        ' xlSheetVisible
Private Const SHEET_HIDDEN As Integer = 0          ' xlSheetHidden
Private Const SHEET_VERY_HIDDEN As Integer = 2     ' xlSheetVeryHidden

' =============================================================================
' 主要功能函数
' =============================================================================

' 安全退出：隐藏除"主页"外的所有工作表
Public Sub HideAllSheetsExceptHomePage()
    On Error Resume Next

    Dim ws As Worksheet
    Dim homePageNames As Variant
    Dim i As Integer
    Dim isHomePage As Boolean

    Application.ScreenUpdating = False

    ' 定义可能的主页名称（支持多种命名方式）
    homePageNames = Array("主页", "首页", "Home", "HomePage", "Main", "主界面", "登录页", "Login")

    ' 遍历所有工作表
    For Each ws In ThisWorkbook.Worksheets
        isHomePage = False

        ' 检查是否为主页
        For i = LBound(homePageNames) To UBound(homePageNames)
            If InStr(1, ws.Name, homePageNames(i), vbTextCompare) > 0 Then
                isHomePage = True
                Exit For
            End If
        Next i

        ' 如果不是主页，则隐藏
        If Not isHomePage Then
            ws.Visible = SHEET_VERY_HIDDEN
        Else
            ' 确保主页可见
            ws.Visible = SHEET_VISIBLE
        End If
    Next ws

    ' 如果没有找到主页，保留第一个工作表可见
    If CountVisibleSheets() = 0 And ThisWorkbook.Worksheets.Count > 0 Then
        ThisWorkbook.Worksheets(1).Visible = SHEET_VISIBLE
    End If

    Application.ScreenUpdating = True

    ' 记录安全操作
    Call LogSecurityEvent("SECURITY", "已隐藏除主页外的所有工作表")
End Sub

' 激活主页工作表（登录成功后调用）
Public Sub ActivateHomePage()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Dim homePageNames As Variant
    Dim i As Integer
    Dim homePageFound As Boolean

    ' 定义可能的主页名称（支持多种命名方式）
    homePageNames = Array("主页", "首页", "Home", "HomePage", "Main", "主界面")
    homePageFound = False

    ' 遍历所有工作表，查找主页
    For Each ws In ThisWorkbook.Worksheets
        For i = LBound(homePageNames) To UBound(homePageNames)
            If InStr(1, ws.Name, homePageNames(i), vbTextCompare) > 0 Then
                ' 找到主页，激活它
                ws.Activate
                homePageFound = True
                Call LogSecurityEvent("INFO", "已激活主页工作表: " & ws.Name)
                Exit Sub
            End If
        Next i
    Next ws

    ' 如果没有找到主页，激活第一个可见工作表
    If Not homePageFound Then
        For Each ws In ThisWorkbook.Worksheets
            If ws.Visible = SHEET_VISIBLE Then
                ws.Activate
                Call LogSecurityEvent("WARNING", "未找到主页，已激活工作表: " & ws.Name)
                Exit Sub
            End If
        Next ws
    End If

    Exit Sub

ErrorHandler:
    Call LogSecurityEvent("ERROR", "激活主页失败: " & Err.Description)
End Sub

' 显示所有工作表（离线模式使用）
Public Sub ShowAllSheets()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Dim systemSheetNames As Variant
    Dim i As Integer
    Dim isSystemSheet As Boolean
    Dim sheetCount As Integer

    Application.ScreenUpdating = False
    sheetCount = 0

    ' 定义系统工作表名称（这些表保持隐藏）
    systemSheetNames = Array("系统配置", "用户权限", "Config", "UserRoles", "Logs", "UserToken")

    ' 显示所有非系统工作表
    For Each ws In ThisWorkbook.Worksheets
        isSystemSheet = False

        ' 检查是否为系统工作表
        For i = LBound(systemSheetNames) To UBound(systemSheetNames)
            If InStr(1, ws.Name, systemSheetNames(i), vbTextCompare) > 0 Then
                isSystemSheet = True
                Exit For
            End If
        Next i

        ' 如果不是系统工作表，则显示
        If Not isSystemSheet Then
            ws.Visible = SHEET_VISIBLE
            sheetCount = sheetCount + 1
        End If
    Next ws

    Application.ScreenUpdating = True

    ' 记录操作
    Call LogSecurityEvent("INFO", "离线模式：已显示 " & sheetCount & " 个工作表")

    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Call LogSecurityEvent("ERROR", "显示所有工作表失败: " & Err.Description)
End Sub

' 隐藏所有工作表（登录前调用）
Public Sub HideAllSheets()
    On Error Resume Next

    Dim ws As Worksheet
    Dim sheetCount As Integer
    Dim visibleCount As Integer

    Application.ScreenUpdating = False

    ' 统计当前可见的工作表数量
    visibleCount = 0
    For Each ws In ThisWorkbook.Worksheets
        If ws.Visible = SHEET_VISIBLE Then
            visibleCount = visibleCount + 1
        End If
    Next ws

    ' 确保至少保留一个可见工作表（Excel要求）
    sheetCount = 0
    For Each ws In ThisWorkbook.Worksheets
        sheetCount = sheetCount + 1

        ' 如果只有一个工作表，则不能隐藏
        If ThisWorkbook.Worksheets.Count = 1 Then
            Exit Sub
        End If

        ' 隐藏工作表为VeryHidden状态
        If ws.Visible = SHEET_VISIBLE Then
            ' 保留最后一个可见工作表，其他全部隐藏
            If visibleCount > 1 Then
                ws.Visible = SHEET_VERY_HIDDEN
                visibleCount = visibleCount - 1
            End If
        End If
    Next ws

    Application.ScreenUpdating = True
End Sub



' 显示指定的工作表列表
Public Sub ShowSpecificSheets(sheetNames As Variant)
    On Error Resume Next

    Dim ws As Worksheet
    Dim i As Integer
    Dim sheetName As String

    Application.ScreenUpdating = False

    ' 如果传入的是数组
    If IsArray(sheetNames) Then
        For i = LBound(sheetNames) To UBound(sheetNames)
            sheetName = CStr(sheetNames(i))
            Set ws = GetWorksheetByName(sheetName)
            If Not ws Is Nothing Then
                ws.Visible = SHEET_VISIBLE
            End If
        Next i
    Else
        ' 如果传入的是单个工作表名称
        sheetName = CStr(sheetNames)
        Set ws = GetWorksheetByName(sheetName)
        If Not ws Is Nothing Then
            ws.Visible = SHEET_VISIBLE
        End If
    End If

    Application.ScreenUpdating = True
End Sub

' 隐藏指定的工作表列表
Public Sub HideSpecificSheets(sheetNames As Variant)
    On Error Resume Next

    Dim ws As Worksheet
    Dim i As Integer
    Dim sheetName As String

    Application.ScreenUpdating = False

    ' 如果传入的是数组
    If IsArray(sheetNames) Then
        For i = LBound(sheetNames) To UBound(sheetNames)
            sheetName = CStr(sheetNames(i))
            Set ws = GetWorksheetByName(sheetName)
            If Not ws Is Nothing Then
                ws.Visible = SHEET_VERY_HIDDEN
            End If
        Next i
    Else
        ' 如果传入的是单个工作表名称
        sheetName = CStr(sheetNames)
        Set ws = GetWorksheetByName(sheetName)
        If Not ws Is Nothing Then
            ws.Visible = SHEET_VERY_HIDDEN
        End If
    End If

    Application.ScreenUpdating = True
End Sub

' =============================================================================
' 工作表访问控制
' =============================================================================

' 根据用户权限显示工作表（登录成功后调用）
Public Sub ShowSheetsForUser(username As String, hotelName As String)
    On Error Resume Next

    ' 记录用户访问 待实现

    ' 显示所有工作表（除了可能的隐藏系统表）
    Call ShowAllSheetsSecurely

    ' 可以根据用户权限或酒店类型进行定制
    ' 示例：根据酒店类型显示不同的工作表
    ' If InStr(hotelName, "希尔顿") > 0 Then
    '     Call ShowSpecificSheets(Array("1.酒店信息", "2.预算信息", "3.市场计划"))
    ' End If

    ' 记录成功访问 待实现

End Sub

' 安全地显示所有工作表
Private Sub ShowAllSheetsSecurely()
    On Error Resume Next

    Dim ws As Worksheet
    Dim systemSheetNames As Variant
    Dim i As Integer
    Dim isSystemSheet As Boolean

    Application.ScreenUpdating = False

    ' 定义系统工作表名称（这些表保持隐藏）
    systemSheetNames = Array("系统配置", "用户权限",  "Config", "UserRoles", "Logs")

    ' 显示所有非系统工作表
    For Each ws In ThisWorkbook.Worksheets
        isSystemSheet = False

        ' 检查是否为系统工作表
        For i = LBound(systemSheetNames) To UBound(systemSheetNames)
            If InStr(1, ws.Name, systemSheetNames(i), vbTextCompare) > 0 Then
                isSystemSheet = True
                Exit For
            End If
        Next i

        ' 如果不是系统工作表，则显示
        If Not isSystemSheet Then
            ws.Visible = SHEET_VISIBLE
        End If
    Next ws

    Application.ScreenUpdating = True
End Sub

' 检查用户是否有访问指定工作表的权限
Public Function HasSheetAccess(username As String, sheetName As String) As Boolean
    On Error Resume Next

    ' 默认所有登录用户都有访问权限
    ' 可以根据需要实现更复杂的权限控制
    HasSheetAccess = IsUserLoggedIn()
End Function

' =============================================================================
' 辅助函数
' =============================================================================

' 根据名称获取工作表对象
Private Function GetWorksheetByName(sheetName As String) As Worksheet
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets(sheetName)
    Set GetWorksheetByName = ws
End Function

' 获取所有可见工作表的名称列表
Public Function GetVisibleSheetNames() As Collection
    On Error Resume Next

    Dim ws As Worksheet
    Dim sheetNames As Collection

    Set sheetNames = New Collection

    For Each ws In ThisWorkbook.Worksheets
        If ws.Visible = SHEET_VISIBLE Then
            sheetNames.Add ws.Name
        End If
    Next ws

    Set GetVisibleSheetNames = sheetNames
End Function

' 统计当前可见工作表的数量
Public Function CountVisibleSheets() As Integer
    On Error Resume Next

    Dim ws As Worksheet
    Dim count As Integer

    count = 0

    For Each ws In ThisWorkbook.Worksheets
        If ws.Visible = SHEET_VISIBLE Then
            count = count + 1
        End If
    Next ws

    CountVisibleSheets = count

    If Err.Number <> 0 Then
        CountVisibleSheets = 0
        Err.Clear
    End If
End Function







