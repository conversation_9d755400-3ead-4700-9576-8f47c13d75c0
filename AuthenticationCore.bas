Option Explicit

' =============================================================================
' 用户认证核心模块 - 希尔顿酒店管理系统
' 提供用户登录、认证、版本检查等功能
' =============================================================================

' =============================================================================
' 模块级变量声明
' =============================================================================

' 全局用户信息变量（用于向后兼容）
Private g_CurrentUser As String           ' 当前用户名（已弃用，使用TokenManager替代）

' =============================================================================
' 用户认证函数
' =============================================================================

' 验证用户登录凭据（优化版：使用Token机制）
Public Function AuthenticateUser(username As String, password As String) As Boolean
    On Error GoTo ErrorHandler

    Dim rs As Object
    Dim sql As String

    AuthenticateUser = False

    ' 构建查询SQL - 只验证用户名和密码
    sql = "SELECT Username, HotelName, HotelInncode, IsActive " & _
          "FROM hilton_users " & _
          "WHERE Username = '" & EscapeStringOnly(username) & "' " & _
          "AND Password = '" & EscapeStringOnly(password) & "' " & _
          "AND IsActive = 1"

    ' 执行查询
    Set rs = ExecuteQuery(sql)

    If rs Is Nothing Then
        Exit Function
    End If

    ' 检查是否找到匹配的用户
    If Not rs.EOF Then
        ' 认证成功，写入Token工作表
        Call WriteUserToken(rs.Fields("Username").Value, _
                           rs.Fields("HotelName").Value, _
                           rs.Fields("HotelInncode").Value)

        ' 更新最后登录时间
        Call UpdateLastLoginTime(username)

        AuthenticateUser = True
    End If

    rs.Close
    Set rs = Nothing
    Exit Function

ErrorHandler:
    AuthenticateUser = False
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close
        Set rs = Nothing
    End If
End Function

' 更新用户最后登录时间
Private Sub UpdateLastLoginTime(username As String)
    On Error Resume Next

    Dim sql As String
    sql = "UPDATE hilton_users SET LastLoginTime = NOW() " & _
          "WHERE Username = '" & EscapeStringOnly(username) & "'"

    Call ExecuteCommand(sql)
End Sub

' 获取用户关联的酒店列表
Public Function GetUserHotels(username As String) As Collection
    On Error GoTo ErrorHandler

    Dim rs As Object
    Dim sql As String
    Dim hotels As Collection

    Set hotels = New Collection
    Set GetUserHotels = hotels

    ' 查询用户可访问的酒店
    sql = "SELECT DISTINCT HotelName FROM hilton_users " & _
          "WHERE Username = '" & EscapeStringOnly(username) & "' " & _
          "AND IsActive = 1"

    Set rs = ExecuteQuery(sql)

    If rs Is Nothing Then
        Exit Function
    End If

    ' 添加酒店到集合
    Do While Not rs.EOF
        hotels.Add rs.Fields("HotelName").Value
        rs.MoveNext
    Loop

    rs.Close
    Set rs = Nothing
    Set GetUserHotels = hotels
    Exit Function

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close
        Set rs = Nothing
    End If
    Set GetUserHotels = New Collection
End Function



' 生成用户会话ID
Public Function GenerateSessionId() As String
    ' 使用TokenManager获取当前用户，替代已弃用的g_CurrentUser变量
    Dim currentUser As String
    currentUser = GetCurrentUser()
    If currentUser = "" Then currentUser = "UNKNOWN"

    GenerateSessionId = "SESSION_" & currentUser & "_" & Format(Now, "yyyymmddhhnnss")
End Function

' 验证密码强度（可选功能）
Public Function ValidatePasswordStrength(password As String) As Boolean
    ' 简单验证：密码长度至少4位
    ValidatePasswordStrength = (Len(password) >= 4)
End Function

' SQL字符串转义（仅转义单引号）
Private Function EscapeStringOnly(inputStr As String) As String
    On Error Resume Next

    If inputStr = "" Then
        EscapeStringOnly = ""
        Exit Function
    End If

    ' 转义单引号
    EscapeStringOnly = Replace(inputStr, "'", "''")
End Function
