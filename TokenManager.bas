Option Explicit

' =============================================================================
' Token管理模块 - 希尔顿酒店管理系统（极简版）
' 只保存用户信息，不进行任何验证（登录成功后无需验证）
' 测试时只在日志sheet进行日志记录，待正式生产再输出至数据库 (关键！)
' =============================================================================



' =============================================================================
' 核心Token管理
' =============================================================================

' 获取Token工作表
Private Function GetTokenSheet() As Worksheet
    On Error GoTo CreateSheet

    Set GetTokenSheet = ThisWorkbook.Worksheets("UserToken")
    GetTokenSheet.Visible = xlSheetVeryHidden
    Exit Function

CreateSheet:
    Set GetTokenSheet = ThisWorkbook.Worksheets.Add
    GetTokenSheet.Name = "UserToken"
    GetTokenSheet.Visible = xlSheetVeryHidden
End Function

' 写入用户Token（登录成功后调用）
Public Sub WriteUserToken(username As String, hotelName As String, hotelInncode As String)
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = GetTokenSheet()

    ws.Range("A1").Value = username
    ws.Range("A2").Value = hotelName
    ws.Range("A3").Value = hotelInncode
    ws.Range("A4").Value = Now
End Sub

' 清除用户Token（退出时调用）
Public Sub ClearUserToken()
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = GetTokenSheet()

    ws.Range("A1:A4").ClearContents
End Sub

' 获取当前用户名（仅用于显示）
Public Function GetCurrentUser() As String
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = GetTokenSheet()

    GetCurrentUser = Trim(ws.Range("A1").Value)
End Function

' 获取当前酒店名称（仅用于显示）
Public Function GetCurrentHotelName() As String
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = GetTokenSheet()

    GetCurrentHotelName = Trim(ws.Range("A2").Value)
End Function

' 获取当前酒店Inn Code（仅用于显示）
Public Function GetCurrentHotelInncode() As String
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = GetTokenSheet()

    GetCurrentHotelInncode = Trim(ws.Range("A3").Value)
End Function

' 检查用户是否已登录（基于Token存在性）
Public Function IsUserLoggedIn() As Boolean
    On Error Resume Next

    Dim ws As Worksheet
    Dim userName As String

    Set ws = GetTokenSheet()
    userName = Trim(ws.Range("A1").Value)

    ' 如果用户名不为空，则认为已登录
    IsUserLoggedIn = (userName <> "")

    If Err.Number <> 0 Then
        IsUserLoggedIn = False
        Err.Clear
    End If
End Function


