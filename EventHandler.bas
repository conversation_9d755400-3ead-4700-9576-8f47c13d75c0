Option Explicit

' =============================================================================
' 事件处理器模块 - 希尔顿酒店管理系统
' 统一处理工作表事件，包括数据变化和按钮点击
' =============================================================================

' 处理工作表数据变化事件（主入口）
Public Sub HandleWorksheetChange(ws As Worksheet, changedRange As Range)
    On Error GoTo ErrorHandler

    ' 使用静态变量防止递归调用
    Static isHandling As Boolean
    If isHandling Then Exit Sub

    isHandling = True

    ' 防止递归调用
    Application.EnableEvents = False

    ' 根据工作表名称分发事件处理
    Select Case ws.Name
        Case "2. 业务背景"
            Call HandleBusinessForecastChange(ws, changedRange)
        Case "1.酒店信息"
            ' 酒店信息工作表的变化处理（如果需要）
            ' Call HandleHotelInfoChange(ws, changedRange)
        Case Else
            ' 其他工作表不处理
    End Select

    Application.EnableEvents = True
    isHandling = False
    Exit Sub

ErrorHandler:
    Application.EnableEvents = True
    isHandling = False
    Call LogManager.LogEvent("ERROR", "工作表变化处理异常: " & Err.Description, ws.Name, "事件处理")
End Sub

' 处理业务预测工作表的数据变化
Private Sub HandleBusinessForecastChange(ws As Worksheet, changedRange As Range)
    On Error GoTo ErrorHandler

    ' 检查版本选择变化
    If changedRange.Address = "$B$4" Then
        Dim newVersion As String
        newVersion = Trim(CStr(changedRange.Value))
        If newVersion <> "" Then
            Call BusinessForecastManager.HandleVersionChange(ws, newVersion)
        End If
        Exit Sub
    End If

    ' 检查E列Add More文字点击（单击触发）
    If changedRange.Column = 5 Then  ' E列
        Call HandleAddMoreCellClick(ws, changedRange)
        Exit Sub
    End If

    ' 检查各个数据区域的变化
    Call BusinessForecastManager.HandleSegmentRatioChange(ws, changedRange)
    Call BusinessForecastManager.HandleChannelRatioChange(ws, changedRange)
    Call BusinessForecastManager.HandleHotelGOBChange(ws, changedRange)
    Call BusinessForecastManager.HandlePersonalTravelPriceChange(ws, changedRange)

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "业务预测数据变化处理异常: " & Err.Description, ws.Name, "事件处理")
End Sub

' 处理工作表双击事件（主入口）
Public Sub HandleWorksheetDoubleClick(ws As Worksheet, Target As Range)
    On Error GoTo ErrorHandler

    ' 根据工作表名称分发事件处理
    Select Case ws.Name
        Case "1.酒店信息"
            Call HotelInfoManager.HandleHotelInfoDoubleClick(Target)
        Case "2. 业务背景"
            ' 检查E列Add More文字双击
            If Target.Column = 5 Then  ' E列
                Call HandleAddMoreCellClick(ws, Target)
            End If
        Case Else
            ' 其他工作表的双击处理
    End Select

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "工作表双击处理异常: " & Err.Description, ws.Name, "事件处理")
End Sub



' 初始化个人旅游价区域
Public Sub InitializePersonalTravelPriceArea()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet("2.业务背景")
    
    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到业务背景工作表", "2.业务背景", "初始化")
        Exit Sub
    End If
    
    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)
    
    ' 设置初始标识
    Dim row As Integer
    For row = 42 To 45  ' 初始4行
        ws.Cells(row, 1).Value = "个人旅游价区域"
    Next row
    
    ' 设置标题行
    ws.Cells(41, 2).Value = "ColB"
    ws.Cells(41, 3).Value = "ColC"
    ws.Cells(41, 4).Value = "ColD"
    ws.Cells(41, 5).Value = "ColE"
    ws.Cells(41, 6).Value = "ColF"
    ws.Cells(41, 7).Value = "ColG"
    ws.Cells(41, 8).Value = "ColH"
    ws.Cells(41, 9).Value = "ColI"
    ws.Cells(41, 10).Value = "ColJ"
    ws.Cells(41, 11).Value = "ColK"
    ws.Cells(41, 12).Value = "ColL"
    
    ' 创建初始合并单元格
    Call RebuildPersonalTravelPriceMerge(ws, 42, 45)
    
    ' 确保Add More按钮存在
    Call EnsureAddMoreButton(ws, "Add_More2", 46)
    
    Call Utils.ProtectWorksheet(ws, wasProtected)
    
    Call LogManager.LogEvent("INFO", "个人旅游价区域初始化完成", "2.业务背景", "初始化")
    Exit Sub
    
ErrorHandler:
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "个人旅游价区域初始化失败: " & Err.Description, "2.业务背景", "初始化")
End Sub

' 重建个人旅游价区域合并单元格
Private Sub RebuildPersonalTravelPriceMerge(ws As Worksheet, startRow As Integer, endRow As Integer)
    On Error Resume Next
    
    ' 先取消现有合并
    ws.Range("B42:B51").UnMerge
    ws.Range("C42:C51").UnMerge
    
    ' 重新合并B列和C列
    If endRow > startRow Then
        ws.Range("B" & startRow & ":B" & endRow).Merge
        ws.Range("C" & startRow & ":C" & endRow).Merge
        
        ' 设置合并单元格的对齐方式
        With ws.Range("B" & startRow & ":B" & endRow)
            .HorizontalAlignment = xlCenter
            .VerticalAlignment = xlCenter
        End With
        
        With ws.Range("C" & startRow & ":C" & endRow)
            .HorizontalAlignment = xlCenter
            .VerticalAlignment = xlCenter
        End With
    End If
End Sub



' 统一处理Add More单元格点击（单击和双击）
Private Sub HandleAddMoreCellClick(ws As Worksheet, Target As Range)
    On Error GoTo ErrorHandler

    ' 使用静态变量防止递归调用
    Static isProcessing As Boolean
    If isProcessing Then
        Call LogManager.LogEvent("DEBUG", "Add More处理中，跳过递归调用", ws.Name, "递归防护")
        Exit Sub
    End If

    isProcessing = True

    ' 检查是否为Add More单元格
    Dim cellValue As String
    cellValue = Trim(CStr(Target.Value))

    ' 如果不是Add More文字，直接退出
    If InStr(LCase(cellValue), "add more") = 0 Then
        isProcessing = False
        Exit Sub
    End If

    ' 检查是哪个区域的Add More
    If IsEventsAddMoreClick(ws, Target.Row) Then
        Call BusinessForecastManager.HandleEventsAddMoreClick(ws)
        ' 恢复Add More文字和格式
        Call RestoreAddMoreText(Target)
        Call LogManager.LogEvent("INFO", "年度重大事件Add More处理完成", ws.Name, "Add More点击")
    ElseIf IsSegmentAddMoreClick(ws, Target.Row) Then
        ' 调用新的窗体管理器显示生意板块选择窗体
        Call SegmentFormManager.ShowSegmentForm
        ' 恢复Add More文字和格式
        Call RestoreAddMoreText(Target)
        Call LogManager.LogEvent("INFO", "生意板块管理窗体显示完成", ws.Name, "Add More点击")
    End If

    isProcessing = False
    Exit Sub

ErrorHandler:
    isProcessing = False
    Call LogManager.LogEvent("ERROR", "Add More单元格点击处理异常: " & Err.Description, ws.Name, "Add More点击")
End Sub

' 恢复Add More文字和格式（与B6单元格一致）
Private Sub RestoreAddMoreText(Target As Range)
    On Error GoTo ErrorHandler

    ' 禁用事件防止递归调用
    Application.EnableEvents = False

    Dim ws As Worksheet
    Set ws = Target.Worksheet

    With Target
        .Value = " + add more "
        ' 复制B6单元格的所有格式
        .Font.Name = ws.Range("B6").Font.Name
        .Font.Size = ws.Range("B6").Font.Size
        .Font.Color = ws.Range("B6").Font.Color
        .Font.Bold = ws.Range("B6").Font.Bold
        .Font.Italic = ws.Range("B6").Font.Italic
        .Interior.Color = ws.Range("B6").Interior.Color
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

    ' 重新启用事件
    Application.EnableEvents = True
    Exit Sub

ErrorHandler:
    ' 确保事件总是被重新启用
    Application.EnableEvents = True
    Call LogManager.LogEvent("ERROR", "恢复Add More文字失败: " & Err.Description, ws.Name, "格式恢复")
End Sub

' 判断是否为年度重大事件区域的Add More点击
Private Function IsEventsAddMoreClick(ws As Worksheet, clickRow As Integer) As Boolean
    On Error Resume Next

    IsEventsAddMoreClick = False

    ' 检查点击行的B列是否包含"年度重大事件"标记
    Dim cellValue As String
    cellValue = Trim(CStr(ws.Cells(clickRow, 2).Value))

    If cellValue = "年度重大事件" Then
        IsEventsAddMoreClick = True
    End If
End Function

' 判断是否为生意板块管理区域的Add More点击
Private Function IsSegmentAddMoreClick(ws As Worksheet, clickRow As Integer) As Boolean
    On Error Resume Next

    IsSegmentAddMoreClick = False

    ' 检查点击行的B列是否包含"生意板块管理"标记
    Dim cellValue As String
    cellValue = Trim(CStr(ws.Cells(clickRow, 2).Value))

    If cellValue = "生意板块管理" Then
        IsSegmentAddMoreClick = True
    End If
End Function


