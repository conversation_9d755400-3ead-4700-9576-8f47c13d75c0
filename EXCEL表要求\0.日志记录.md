# 日志记录功能要求

1. 日志要同时记录到“日志”sheet和数据库logs表，表结构详见“SQL.md”
2.测试阶段只在日志sheet进行日志记录，待正式生产再输出至数据库

## LogManager重构后的设计

### 表结构映射关系
使用logs表结构，字段映射：
- **id**: 自动生成格式为 "LOG-yyyymmddhhnnss-xxxxx" 的唯一标识符
- **inn_code**: 从TokenManager.GetCurrentHotelInncode()获取，用于数据权限控制
- **sheet_name**: 记录操作发生的Sheet名称，默认为当前活动Sheet
- **log_level**: 日志级别（INFO/WARNING/ERROR）
- **log_message**: 详细的日志描述信息
- **remark**: 备注信息，可用于记录操作类型等补充说明
- **created_at**: 数据库自动生成的时间戳

### 主要函数
1. **LogEvent(eventType, message, [sheetName], [remark])** - 主要接口函数
2. **LogSyncEvent(eventType, message, [operationType], [moduleName], [recordID])** - 兼容旧接口
3. **StartNewOperationSession(operationType, [sheetName])** - 开始操作会话
4. **EndCurrentOperationSession(operationType, [status], [sheetName])** - 结束操作会话
5. **ShowDatabaseLogStatistics()** - 查看数据库日志统计
6. **GoToLogSheet()** - 导航到Excel日志工作表
7. **TestLogManager()** - 综合测试函数

### 使用示例
```vba
' 基本日志记录
Call LogEvent("INFO", "用户登录成功", "登录", "用户验证通过")
Call LogEvent("ERROR", "保存失败", "1.酒店信息", "数据库连接超时")

' 操作会话管理
Call StartNewOperationSession("数据同步", "1.酒店信息")
Call LogEvent("INFO", "同步完成", "1.酒店信息", "成功同步5条记录")
Call EndCurrentOperationSession("数据同步", "SUCCESS", "1.酒店信息")

' 兼容旧接口
Call LogSyncEvent("INFO", "数据同步完成", "SYNC", "DataSyncCore")
```

