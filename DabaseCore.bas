Option Explicit

' =============================================================================
' 通用数据库操作核心类 - 纯技术实现
' 提供基础的数据库连接和CRUD操作
' 不包含任何业务逻辑
' =============================================================================

' MySQL连接配置
Public Const DB_SERVER As String = "localhost"  ' "**************"
Public Const DB_PORT As String = "3306"
Public Const DB_NAME As String = "hiltonmarket"
Public Const DB_USER As String = "root"
Public Const DB_PASSWORD As String = "zy123good"

' =============================================================================
' 核心数据库连接函数
' =============================================================================

' 获取标准ODBC连接字符串
Public Function GetConnectionString() As String
    GetConnectionString = "Driver={MySQL ODBC 8.0 Unicode Driver};" & _
                         "Server=" & DB_SERVER & ";" & _
                         "Port=" & DB_PORT & ";" & _
                         "Database=" & DB_NAME & ";" & _
                         "User=" & DB_USER & ";" & _
                         "Password=" & DB_PASSWORD & ";" & _
                         "Option=3;"
End Function

' 测试数据库连接
Public Function TestConnection() As Boolean
    Dim conn As Object
    TestConnection = False

    Set conn = CreateObject("ADODB.Connection")

    On Error GoTo ErrorHandler
    conn.Open GetConnectionString()
    TestConnection = True
    conn.Close
    Set conn = Nothing
    Exit Function

ErrorHandler:
    TestConnection = False
    If Not conn Is Nothing Then
        If conn.State = 1 Then conn.Close
        Set conn = Nothing
    End If
End Function

' 执行SQL查询并返回记录集
Public Function ExecuteQuery(sql As String) As Object
    Dim conn As Object
    Dim rs As Object

    Set conn = CreateObject("ADODB.Connection")
    Set rs = CreateObject("ADODB.Recordset")

    On Error GoTo ErrorHandler

    conn.Open GetConnectionString()
    rs.Open sql, conn, 1, 1  ' adOpenKeyset, adLockReadOnly

    Set ExecuteQuery = rs
    ' 注意：连接对象会在记录集关闭时自动关闭
    Exit Function

ErrorHandler:
    Set ExecuteQuery = Nothing
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close
        Set rs = Nothing
    End If
    If Not conn Is Nothing Then
        If conn.State = 1 Then conn.Close
        Set conn = Nothing
    End If
    Err.Raise Err.Number, Err.Source, "查询执行失败: " & Err.Description
End Function

' 执行SQL命令（INSERT, UPDATE, DELETE）
Public Function ExecuteCommand(sql As String) As Boolean
    Dim conn As Object
    ExecuteCommand = False

    Set conn = CreateObject("ADODB.Connection")

    On Error GoTo ErrorHandler

    conn.Open GetConnectionString()
    conn.Execute sql
    ExecuteCommand = True

    conn.Close
    Set conn = Nothing
    Exit Function

ErrorHandler:
    ExecuteCommand = False
    If Not conn Is Nothing Then
        If conn.State = 1 Then conn.Close
        Set conn = Nothing
    End If
    Err.Raise Err.Number, Err.Source, "命令执行失败: " & Err.Description
End Function

' 获取单个值（标量查询）
Public Function ExecuteScalar(sql As String) As Variant
    Dim rs As Object

    On Error GoTo ErrorHandler

    Set rs = ExecuteQuery(sql)

    If rs Is Nothing Or rs.EOF Then
        ExecuteScalar = Null
    Else
        ExecuteScalar = rs.fields(0).value
    End If

    If Not rs Is Nothing Then
        rs.Close
        Set rs = Nothing
    End If
    Exit Function

ErrorHandler:
    ExecuteScalar = Null
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close
        Set rs = Nothing
    End If
End Function


' =============================================================================
' 通用CRUD操作
' =============================================================================

' 插入数据
Public Function InsertData(tableName As String, fields As String, values As String) As Boolean
    Dim sql As String

    sql = "INSERT INTO " & tableName & " (" & fields & ") VALUES (" & values & ")"

    On Error GoTo ErrorHandler
    InsertData = ExecuteCommand(sql)
    Exit Function

ErrorHandler:
    InsertData = False
    Err.Raise Err.Number, Err.Source, "插入数据失败: " & Err.Description
End Function

' 更新数据
Public Function UpdateData(tableName As String, setClause As String, whereClause As String) As Boolean
    Dim sql As String

    sql = "UPDATE " & tableName & " SET " & setClause
    If whereClause <> "" Then
        sql = sql & " WHERE " & whereClause
    End If

    On Error GoTo ErrorHandler
    UpdateData = ExecuteCommand(sql)
    Exit Function

ErrorHandler:
    UpdateData = False
    Err.Raise Err.Number, Err.Source, "更新数据失败: " & Err.Description
End Function

' 删除数据
Public Function DeleteData(tableName As String, whereClause As String) As Boolean
    Dim sql As String

    sql = "DELETE FROM " & tableName
    If whereClause <> "" Then
        sql = sql & " WHERE " & whereClause
    End If

    On Error GoTo ErrorHandler
    DeleteData = ExecuteCommand(sql)
    Exit Function

ErrorHandler:
    DeleteData = False
    Err.Raise Err.Number, Err.Source, "删除数据失败: " & Err.Description
End Function

' 查询数据（返回记录集）
Public Function SelectData(tableName As String, Optional fields As String = "*", Optional whereClause As String = "", Optional orderBy As String = "") As Object
    Dim sql As String

    sql = "SELECT " & fields & " FROM " & tableName

    If whereClause <> "" Then
        sql = sql & " WHERE " & whereClause
    End If

    If orderBy <> "" Then
        sql = sql & " ORDER BY " & orderBy
    End If

    Set SelectData = ExecuteQuery(sql)
End Function

' =============================================================================
' 静默执行函数（用于数据同步）
' =============================================================================

' 静默执行SQL命令（不显示错误对话框）
Public Function ExecuteCommandSilent(sql As String) As Boolean
    Dim conn As Object
    ExecuteCommandSilent = False

    Set conn = CreateObject("ADODB.Connection")

    On Error GoTo ErrorHandler

    conn.Open GetConnectionString()
    conn.Execute sql
    ExecuteCommandSilent = True

    conn.Close
    Set conn = Nothing
    Exit Function

ErrorHandler:
    ExecuteCommandSilent = False
    If Not conn Is Nothing Then
        If conn.State = 1 Then conn.Close
        Set conn = Nothing
    End If
    ' 静默处理错误，不显示对话框
End Function

' 增强版静默执行SQL命令（返回详细错误信息）
Public Function ExecuteCommandWithDetails(sql As String, ByRef errorMsg As String) As Boolean
    Dim conn As Object
    ExecuteCommandWithDetails = False
    errorMsg = ""

    Set conn = CreateObject("ADODB.Connection")

    On Error GoTo ErrorHandler

    conn.Open GetConnectionString()
    conn.Execute sql
    ExecuteCommandWithDetails = True

    conn.Close
    Set conn = Nothing
    Exit Function

ErrorHandler:
    ExecuteCommandWithDetails = False

    ' 捕获详细错误信息
    errorMsg = "错误号: " & Err.Number & vbCrLf & _
               "错误描述: " & Err.Description & vbCrLf & _
               "错误来源: " & Err.Source

    ' 如果是ADO错误，尝试获取更多信息
    If Not conn Is Nothing Then
        On Error Resume Next
        If conn.Errors.Count > 0 Then
            Dim i As Integer
            errorMsg = errorMsg & vbCrLf & "ADO错误详情:"
            For i = 0 To conn.Errors.Count - 1
                errorMsg = errorMsg & vbCrLf & _
                          "  错误 " & (i + 1) & ": " & conn.Errors(i).Description & _
                          " (代码: " & conn.Errors(i).Number & ")"
            Next i
        End If
        On Error GoTo 0

        If conn.State = 1 Then conn.Close
        Set conn = Nothing
    End If
End Function

' 静默执行查询（不显示错误对话框）
Public Function ExecuteQuerySilent(sql As String) As Object
    Dim conn As Object
    Dim rs As Object

    Set conn = CreateObject("ADODB.Connection")
    Set rs = CreateObject("ADODB.Recordset")

    On Error GoTo ErrorHandler

    conn.Open GetConnectionString()
    rs.Open sql, conn, 1, 1  ' adOpenKeyset, adLockReadOnly

    Set ExecuteQuerySilent = rs
    Exit Function

ErrorHandler:
    Set ExecuteQuerySilent = Nothing
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close
        Set rs = Nothing
    End If
    If Not conn Is Nothing Then
        If conn.State = 1 Then conn.Close
        Set conn = Nothing
    End If
    ' 静默处理错误，不显示对话框
End Function

' 静默执行标量查询（不显示错误对话框）
Public Function ExecuteScalarSilent(sql As String) As Variant
    Dim rs As Object

    On Error GoTo ErrorHandler

    Set rs = ExecuteQuerySilent(sql)

    If rs Is Nothing Or rs.EOF Then
        ExecuteScalarSilent = Null
    Else
        ExecuteScalarSilent = rs.Fields(0).Value
    End If

    If Not rs Is Nothing Then
        rs.Close
        Set rs = Nothing
    End If
    Exit Function

ErrorHandler:
    ExecuteScalarSilent = Null
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close
        Set rs = Nothing
    End If
    ' 静默处理错误，不显示对话框
End Function

' 检查记录是否存在
Public Function RecordExists(tableName As String, whereClause As String) As Boolean
    Dim sql As String
    Dim count As Variant

    sql = "SELECT COUNT(*) FROM " & tableName & " WHERE " & whereClause
    count = ExecuteScalar(sql)

    RecordExists = (count > 0)
End Function

' 获取记录数量
Public Function GetRecordCount(tableName As String, Optional whereClause As String = "") As Long
    Dim sql As String
    Dim count As Variant

    sql = "SELECT COUNT(*) FROM " & tableName
    If whereClause <> "" Then
        sql = sql & " WHERE " & whereClause
    End If

    count = ExecuteScalar(sql)

    If IsNull(count) Then
        GetRecordCount = 0
    Else
        GetRecordCount = CLng(count)
    End If
End Function

' =============================================================================
' 辅助函数
' =============================================================================

' 安全的字符串转义（防止SQL注入）
Public Function EscapeString(inputStr As String) As String
    EscapeString = "'" & Replace(inputStr, "'", "''") & "'"
End Function

' 转义字符串（不加引号）
Public Function EscapeStringOnly(inputStr As String) As String
    EscapeStringOnly = Replace(inputStr, "'", "''")
End Function

' 构建WHERE条件（LIKE查询）
Public Function BuildLikeCondition(fieldName As String, searchValue As String) As String
    BuildLikeCondition = fieldName & " LIKE '%" & EscapeStringOnly(searchValue) & "%'"
End Function

' 构建WHERE条件（等值查询）
Public Function BuildEqualCondition(fieldName As String, value As String) As String
    BuildEqualCondition = fieldName & " = " & EscapeString(value)
End Function

' 构建多字段OR条件
Public Function BuildOrCondition(fieldNames As Variant, searchValue As String) As String
    Dim i As Integer
    Dim conditions() As String

    ReDim conditions(UBound(fieldNames))

    For i = 0 To UBound(fieldNames)
        conditions(i) = BuildLikeCondition(fieldNames(i), searchValue)
    Next i

    BuildOrCondition = "(" & Join(conditions, " OR ") & ")"
End Function

' 验证表名（基本安全检查）
Public Function IsValidTableName(tableName As String) As Boolean
    ' 简单的表名验证：只允许字母、数字、下划线
    Dim i As Integer
    Dim char As String

    If Len(tableName) = 0 Or Len(tableName) > 64 Then
        IsValidTableName = False
        Exit Function
    End If

    For i = 1 To Len(tableName)
        char = Mid(tableName, i, 1)
        If Not ((char >= "a" And char <= "z") Or _
                (char >= "A" And char <= "Z") Or _
                (char >= "0" And char <= "9") Or _
                char = "_") Then
            IsValidTableName = False
            Exit Function
        End If
    Next i

    IsValidTableName = True
End Function

' 获取表结构信息
Public Function GetTableStructure(tableName As String) As Object
    Dim sql As String

    sql = "DESCRIBE " & tableName
    Set GetTableStructure = ExecuteQuery(sql)
End Function

' 检查表是否存在
Public Function TableExists(tableName As String) As Boolean
    Dim sql As String
    Dim count As Variant

    sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '" & DB_NAME & "' AND table_name = '" & tableName & "'"
    count = ExecuteScalar(sql)

    TableExists = (count > 0)
End Function


