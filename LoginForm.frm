VERSION 5.00
Begin {C62A69F0-16DC-11CE-9E98-00AA00574A4F} LoginForm 
   Caption         =   "Login Form"
   ClientHeight    =   3510
   ClientLeft      =   45
   ClientTop       =   390
   ClientWidth     =   5880
   OleObjectBlob   =   "LoginForm.frx":0000
   StartUpPosition =    2     ' CenterScreen
End
Attribute VB_Name = "LoginForm"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False


Option Explicit

' =============================================================================
' 模块级变量声明
' =============================================================================

' 登录尝试次数控制变量
Private loginAttempts As Integer           ' 当前登录尝试次数
Private maxLoginAttempts As Integer        ' 最大允许登录尝试次数

' =============================================================================
' 表单事件
' =============================================================================

Private Sub UserForm_Initialize()
    On Error GoTo ErrorHandler

    ' 初始化表单
    Call SetupForm
    ' 注意：LoadHotelList 已移除，因为不再需要酒店选择

    ' 初始化登录尝试次数
    loginAttempts = 0
    maxLoginAttempts = 3

    Exit Sub

ErrorHandler:
    MsgBox "登录表单初始化失败: " & Err.Description, vbCritical, "系统错误"
    Unload Me
End Sub

Private Sub UserForm_Activate()
    On Error Resume Next
    ' 设置焦点到用户名输入框
    txtUsername.SetFocus
End Sub

' =============================================================================
' 控件事件
' =============================================================================

Private Sub btnLogin_Click()
    On Error GoTo ErrorHandler

    Call DoLogin
    Exit Sub

ErrorHandler:
    MsgBox "登录过程中发生错误: " & Err.Description, vbCritical, "登录错误"
End Sub

Private Sub BtnCancel_Click()
    ' 取消登录，关闭工作簿
    Application.DisplayAlerts = False
    ThisWorkbook.Close SaveChanges:=False
    Application.DisplayAlerts = True
End Sub

Private Sub txtPassword_KeyPress(ByVal KeyAscii As MSForms.ReturnInteger)
    ' 在密码框中按回车键时执行登录
    If KeyAscii = 13 Then ' Enter键
        Call DoLogin
    End If
End Sub

Private Sub txtUsername_KeyPress(ByVal KeyAscii As MSForms.ReturnInteger)
    ' 在用户名框中按回车键时移动到密码框
    If KeyAscii = 13 Then ' Enter键
        txtPassword.SetFocus
    End If
End Sub

' =============================================================================
' 表单设置
' =============================================================================

Private Sub SetupForm()
    On Error Resume Next

    ' 设置表单属性
    Me.Caption = "希尔顿酒店管理系统 - 用户登录"
    Me.Width = 320
    Me.Height = 220  ' 减少高度，因为去掉了酒店选择

    ' 设置控件属性（假设控件已在设计时创建）
    With txtUsername
        .Text = ""
        .MaxLength = 50
        .TabIndex = 0
    End With

    With txtPassword
        .Text = ""
        .MaxLength = 50
        .PasswordChar = "*"
        .TabIndex = 1
    End With

    With btnLogin
        .Caption = "登录"
        .Default = True
        .TabIndex = 2
    End With

    With btnCancel
        .Caption = "取消"
        .Cancel = True
        .TabIndex = 3
    End With

    ' 设置标签文本
    lblUsername.Caption = "用户名:"
    lblPassword.Caption = "密码:"
    lblStatus.Caption = "请输入登录信息"
    lblStatus.ForeColor = RGB(0, 0, 0)
End Sub

' 注意：已移除酒店列表加载功能，因为登录不再需要选择酒店

' =============================================================================
' 登录逻辑
' =============================================================================

Private Sub DoLogin()
    On Error GoTo ErrorHandler

    Dim userName As String
    Dim password As String

    ' 获取输入值
    userName = Trim(txtUsername.Text)
    password = Trim(txtPassword.Text)

    ' 验证输入
    If Not ValidateInput(userName, password) Then
        Exit Sub
    End If

    ' 显示登录状态
    lblStatus.Caption = "正在验证用户信息..."
    lblStatus.ForeColor = RGB(0, 0, 255)
    DoEvents

    ' 执行用户认证（只需用户名和密码）
    If AuthenticateUser(userName, password) Then
        ' 登录成功
        lblStatus.Caption = "登录成功！正在加载系统..."
        lblStatus.ForeColor = RGB(0, 128, 0)
        DoEvents

        ' 执行登录成功后的自动化流程
        Call ExecutePostLoginAutomation(userName, GetCurrentHotelName())

        ' 关闭登录表单
        Unload Me

    Else
        ' 登录失败
        loginAttempts = loginAttempts + 1

        lblStatus.Caption = "登录失败！用户名或密码不正确。"
        lblStatus.ForeColor = RGB(255, 0, 0)

        ' 清空密码框
        txtPassword.Text = ""
        txtPassword.SetFocus

        ' 检查登录尝试次数
        If loginAttempts >= maxLoginAttempts Then
            MsgBox "登录失败次数过多，系统将关闭。", vbCritical, "安全提示"
            Application.DisplayAlerts = False
            ThisWorkbook.Close SaveChanges:=False
            Application.DisplayAlerts = True
        End If
    End If

    Exit Sub

ErrorHandler:
    lblStatus.Caption = "登录过程中发生错误: " & Err.Description
    lblStatus.ForeColor = RGB(255, 0, 0)
End Sub

' 验证输入信息
Private Function ValidateInput(userName As String, password As String) As Boolean
    ValidateInput = False

    If userName = "" Then
        lblStatus.Caption = "请输入用户名"
        lblStatus.ForeColor = RGB(255, 0, 0)
        txtUsername.SetFocus
        Exit Function
    End If

    If password = "" Then
        lblStatus.Caption = "请输入密码"
        lblStatus.ForeColor = RGB(255, 0, 0)
        txtPassword.SetFocus
        Exit Function
    End If

    ValidateInput = True
End Function



' 执行登录成功后的自动化流程（集成新的欢迎页管理）
Private Sub ExecutePostLoginAutomation(userName As String, hotelName As String)
    On Error GoTo ErrorHandler

    ' 1. 显示工作表（保留原有逻辑以确保基本功能）
    Call ShowSheetsForUser(userName, hotelName)

    ' 2. 把inncode所在区域写入“1.酒店信息”的D,数据来源于TokenManager
    Call WriteInncodeToHotelInfoSheet

    ' 3. 启动新的欢迎页流程（替代原有的直接跳转和同步）
    Call WelcomePageManager.OnLoginSuccess

    ' 记录登录成功事件
    Call LogSecurityEvent("INFO", "用户登录成功，已启动欢迎页流程: " & userName)

    Exit Sub

ErrorHandler:
    ' 即使发生错误也要确保基本功能可用
    Call ShowSheetsForUser(userName, hotelName)
    Call ActivateHomePage
    Call LogSecurityEvent("ERROR", "登录后自动化流程异常: " & Err.Description & " (用户: " & userName & ")")

    ' 如果欢迎页流程失败，回退到传统流程
    Call SyncAllDataSilently
End Sub

' 把inncode所在区域写入“1.酒店信息”的D
Private Sub WriteInncodeToHotelInfoSheet()
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("1.酒店信息")

    ws.Range("D6").Value = TokenManager.GetCurrentHotelInncode()
    ws.Range("D7").Formula = "=XLOOKUP($D$6,hotelinfo!$A:$A,hotelinfo!$C:$C,"""")"
    ws.Range("D8").Formula = "=XLOOKUP($D$6,hotelinfo!$A:$A,hotelinfo!$G:$G,"""")"
End Sub
