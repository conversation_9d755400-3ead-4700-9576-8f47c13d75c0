
# 2. 业务背景sheet中生意板块管理区域弹出窗口方案设计

## 窗口弹出事件
1.点击生意板块管理同行的E列的 add more时,弹出SegementForm窗体

## 界面控件名称
0.form名称:SegementForm
1.第一层单选按钮控件名称为: OptionRoom\OptionFB\OptionGME\OptionOther
2.第二层单选按钮控件名称为:OptionAwareness\OptionConsideration\OptionConversion
3.第三层复选框控件名称为:CheckBoxBusinessTravelers\CheckBoxFamilyTravelers\CheckBoxMillennialsGenZ\CheckBoxSeniorTravelers\CheckBoxLuxuryMICE\CheckBoxConference\CheckBoxHiltonHonorsMember\CheckBoxFoodies\CheckBoxCouples
4.确定按钮:BtnOk  取消按钮:BtnCancle

## 要求:
1.点击生意板块管理同行的E列的 add more时,弹出SegementForm窗体
2.点击确定按钮时,将数据写入"2. 业务背景"sheet的最后一行(Range("b1000").end来判断最后一行)
3.第一层单选按钮对应的caption值写入最后一行的B列
4.第二层单选按钮对应的caption值写入最后一行的D列
5.第三层复选框选中的caption值用逗号拼接写入最后一行的E列
6.注意，工作表设定了保护，要解除保护后操作，操作完要恢复保护
7.尤其注意: 现在的代码中已经存在点击 生意板块管理同行的E列的 add more事件代码,取消之前的add more代码,使用新的代码
8.在单独的文件中实现,不要在再放入BusinessForecastManager中
9.能服用其他模块中代码的尽量复用,尤其是BusinessForecastManager中的方法,最大限度保持代码简洁
10.不要写说明文档\方案文档\测试文档
