/*
 Navicat Premium Data Transfer

 Source Server         : 本地mysql
 Source Server Type    : MySQL
 Source Server Version : 50716
 Source Host           : localhost:3306
 Source Schema         : hiltonmarket

 Target Server Type    : MySQL
 Target Server Version : 50716
 File Encoding         : 65001

 Date: 12/06/2025 19:41:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for business_forecast
-- ----------------------------
DROP TABLE IF EXISTS `business_forecast`;
CREATE TABLE `business_forecast`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录唯一ID',
  `inn_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '酒店Inn Code，用于数据权限验证',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预算版本：2025年预算, RF1, RF2, ..., RF12',
  `category` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预测类别：OCC, ADR, RevPAR',
  `month_1` decimal(10, 2) DEFAULT NULL COMMENT '1月数据',
  `month_2` decimal(10, 2) DEFAULT NULL COMMENT '2月数据',
  `month_3` decimal(10, 2) DEFAULT NULL COMMENT '3月数据',
  `month_4` decimal(10, 2) DEFAULT NULL COMMENT '4月数据',
  `month_5` decimal(10, 2) DEFAULT NULL COMMENT '5月数据',
  `month_6` decimal(10, 2) DEFAULT NULL COMMENT '6月数据',
  `month_7` decimal(10, 2) DEFAULT NULL COMMENT '7月数据',
  `month_8` decimal(10, 2) DEFAULT NULL COMMENT '8月数据',
  `month_9` decimal(10, 2) DEFAULT NULL COMMENT '9月数据',
  `month_10` decimal(10, 2) DEFAULT NULL COMMENT '10月数据',
  `month_11` decimal(10, 2) DEFAULT NULL COMMENT '11月数据',
  `month_12` decimal(10, 2) DEFAULT NULL COMMENT '12月数据',
  `is_submitted` tinyint(1) DEFAULT 0 COMMENT '是否已提交（仅对2025年预算有效）',
  `created_at` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_forecast`(`inn_code`, `version`, `category`) USING BTREE COMMENT '确保每个酒店每个版本每个类别只有一条记录'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店生意预测表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of business_forecast
-- ----------------------------
INSERT INTO `business_forecast` VALUES ('BF-20250611100712-8144', 'AVAHS', 'RF2', 'OCC', 2.22, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0, '2025-06-11 10:07:13', '2025-06-11 10:13:16');
INSERT INTO `business_forecast` VALUES ('BF-20250611100713-0453', 'AVAHS', 'RF2', 'RevPAR', 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0, '2025-06-11 10:07:14', '2025-06-11 10:13:17');
INSERT INTO `business_forecast` VALUES ('BF-20250611100713-7090', 'AVAHS', 'RF2', 'ADR', 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0, '2025-06-11 10:07:13', '2025-06-11 10:13:17');
INSERT INTO `business_forecast` VALUES ('BF-20250611102616-0562', 'AVAHS', '2025年预算', 'OCC', 0.28, 0.34, 0.34, 0.31, 0.37, 0.43, 0.50, 0.61, 0.71, 0.64, 0.42, 0.32, 1, '2025-06-11 10:26:16', '2025-06-11 10:26:17');
INSERT INTO `business_forecast` VALUES ('BF-20250611102616-9495', 'AVAHS', '2025年预算', 'ADR', 580.00, 450.00, 410.00, 430.00, 500.00, 510.00, 530.00, 630.00, 600.00, 560.00, 450.00, 430.00, 1, '2025-06-11 10:26:17', '2025-06-11 10:26:17');
INSERT INTO `business_forecast` VALUES ('BF-20250611102617-3640', 'AVAHS', '2025年预算', 'RevPAR', 444.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, '2025-06-11 10:26:17', '2025-06-11 10:26:17');

-- ----------------------------
-- Table structure for business_segment_ratio
-- ----------------------------
DROP TABLE IF EXISTS `business_segment_ratio`;
CREATE TABLE `business_segment_ratio`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录唯一ID',
  `inn_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '酒店Inn Code',
  `row_index` int(11) NOT NULL COMMENT '行索引(12-24)',
  `ratio_value` decimal(5, 4) NOT NULL COMMENT '占比值(0-1)',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_segment`(`inn_code`, `row_index`) USING BTREE COMMENT '确保每个酒店每行只有一条记录'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店生意细分占比表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of business_segment_ratio
-- ----------------------------
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110559-1790', 'AVAHS', 13, 0.0100, '2025-06-11 11:05:59', '2025-06-12 16:39:00');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110559-4229', 'AVAHS', 14, 0.3319, '2025-06-11 11:05:59', '2025-06-12 16:39:01');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110559-5431', 'AVAHS', 15, 0.0625, '2025-06-11 11:06:00', '2025-06-12 16:39:01');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110559-6952', 'AVAHS', 12, 0.2500, '2025-06-11 11:05:59', '2025-06-12 16:39:00');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110600-2277', 'AVAHS', 20, 0.0625, '2025-06-11 11:06:00', '2025-06-12 16:39:02');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110600-4275', 'AVAHS', 18, 0.0000, '2025-06-11 11:06:00', '2025-06-12 16:39:01');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110600-5090', 'AVAHS', 19, 0.0000, '2025-06-11 11:06:00', '2025-06-12 16:39:02');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110600-5409', 'AVAHS', 17, 0.2431, '2025-06-11 11:06:00', '2025-06-12 16:39:01');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110600-6191', 'AVAHS', 21, 0.0000, '2025-06-11 11:06:01', '2025-06-12 16:39:02');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110600-8146', 'AVAHS', 16, 0.0000, '2025-06-11 11:06:00', '2025-06-12 16:39:01');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110601-4898', 'AVAHS', 22, 0.0442, '2025-06-11 11:06:01', '2025-06-12 16:39:02');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110601-6808', 'AVAHS', 23, 0.0000, '2025-06-11 11:06:01', '2025-06-12 16:39:02');
INSERT INTO `business_segment_ratio` VALUES ('BSR-20250611110601-8866', 'AVAHS', 24, 0.0000, '2025-06-11 11:06:01', '2025-06-12 16:39:03');

-- ----------------------------
-- Table structure for hilton_users
-- ----------------------------
DROP TABLE IF EXISTS `hilton_users`;
CREATE TABLE `hilton_users`  (
  `Id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `Username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `Password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `HotelName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联酒店名称',
  `HotelInncode` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '酒店Inn Code',
  `IsActive` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `LastLoginTime` timestamp(0) DEFAULT NULL COMMENT '最后登录时间',
  `CreateTime` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `ModifyTime` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `CellAddresses` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '储存单元格地址',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `unique_username`(`Username`) USING BTREE,
  INDEX `idx_username_password`(`Username`, `Password`) USING BTREE,
  INDEX `idx_hotel_name`(`HotelName`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hilton_users
-- ----------------------------
INSERT INTO `hilton_users` VALUES ('1', 'test', '123', 'DoubleTree by Hilton Anshun', 'AVAHS', 1, '2025-06-12 16:38:59', '2025-06-10 06:28:50', '2025-06-12 16:38:59', NULL);
INSERT INTO `hilton_users` VALUES ('2', 'admin', '123', 'admin', 'admin', 1, NULL, '2025-06-10 06:57:22', '2025-06-10 06:57:22', NULL);

-- ----------------------------
-- Table structure for hotel_channel_ratio
-- ----------------------------
DROP TABLE IF EXISTS `hotel_channel_ratio`;
CREATE TABLE `hotel_channel_ratio`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录唯一ID',
  `inn_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '酒店Inn Code',
  `row_index` int(11) NOT NULL COMMENT '行索引(13-17)',
  `ratio_value` decimal(5, 4) NOT NULL COMMENT '占比值(0-1)',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_channel`(`inn_code`, `row_index`) USING BTREE COMMENT '确保每个酒店每行只有一条记录'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店渠道占比表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hotel_channel_ratio
-- ----------------------------
INSERT INTO `hotel_channel_ratio` VALUES ('HCR-20250611115652-0481', 'AVAHS', 14, 0.3020, '2025-06-11 11:56:52', '2025-06-12 16:39:03');
INSERT INTO `hotel_channel_ratio` VALUES ('HCR-20250611115652-1737', 'AVAHS', 13, 0.5300, '2025-06-11 11:56:52', '2025-06-12 16:39:03');
INSERT INTO `hotel_channel_ratio` VALUES ('HCR-20250611115653-5330', 'AVAHS', 16, 0.1400, '2025-06-11 11:56:53', '2025-06-12 16:39:04');
INSERT INTO `hotel_channel_ratio` VALUES ('HCR-20250611115653-5610', 'AVAHS', 17, 0.0020, '2025-06-11 11:56:53', '2025-06-12 16:39:04');
INSERT INTO `hotel_channel_ratio` VALUES ('HCR-20250611115653-7148', 'AVAHS', 15, 0.0300, '2025-06-11 11:56:53', '2025-06-12 16:39:03');

-- ----------------------------
-- Table structure for hotel_gob_analysis
-- ----------------------------
DROP TABLE IF EXISTS `hotel_gob_analysis`;
CREATE TABLE `hotel_gob_analysis`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录唯一ID',
  `inn_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '酒店Inn Code',
  `row_index` int(11) NOT NULL COMMENT '行索引(29-38)',
  `ratio_value` decimal(5, 4) NOT NULL COMMENT '占比值(0-1)',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_gob`(`inn_code`, `row_index`) USING BTREE COMMENT '确保每个酒店每行只有一条记录'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Hotel GOB Analysis表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hotel_gob_analysis
-- ----------------------------
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123125-4016', 'AVAHS', 30, 0.0400, '2025-06-11 12:31:26', '2025-06-12 16:39:04');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123125-6962', 'AVAHS', 29, 0.7500, '2025-06-11 12:31:25', '2025-06-12 16:39:04');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123126-0162', 'AVAHS', 31, 0.0300, '2025-06-11 12:31:26', '2025-06-12 16:39:05');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123126-1061', 'AVAHS', 36, 0.0100, '2025-06-11 12:31:27', '2025-06-12 16:39:06');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123126-1642', 'AVAHS', 33, 0.0200, '2025-06-11 12:31:26', '2025-06-12 16:39:05');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123126-1677', 'AVAHS', 32, 0.0200, '2025-06-11 12:31:26', '2025-06-12 16:39:05');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123126-4060', 'AVAHS', 35, 0.0100, '2025-06-11 12:31:26', '2025-06-12 16:39:05');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123126-5097', 'AVAHS', 34, 0.0100, '2025-06-11 12:31:26', '2025-06-12 16:39:05');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123127-2761', 'AVAHS', 37, 0.0100, '2025-06-11 12:31:27', '2025-06-12 16:39:06');
INSERT INTO `hotel_gob_analysis` VALUES ('HGB-20250611123127-6430', 'AVAHS', 38, 0.0100, '2025-06-11 12:31:27', '2025-06-12 16:39:06');

-- ----------------------------
-- Table structure for hotel_info
-- ----------------------------
DROP TABLE IF EXISTS `hotel_info`;
CREATE TABLE `hotel_info`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录唯一ID',
  `inn_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '酒店Inn Code，用于数据权限验证',
  `plan_owner` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划制定人',
  `hotel_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '酒店名称',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '酒店所属区域',
  `gm_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '总经理',
  `com_director` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商务总监',
  `mkt_mgr` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '市场总监/经理',
  `mecc_contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'MECC联系人',
  `budget_local` decimal(15, 2) DEFAULT NULL COMMENT '酒店本地活动预算',
  `budget_coop` decimal(15, 2) DEFAULT NULL COMMENT '集团市场统筹费（Co-op Fund）',
  `budget_pmp` decimal(15, 2) DEFAULT NULL COMMENT 'PMP预算',
  `budget_total` decimal(15, 2) DEFAULT NULL COMMENT '总预算',
  `created_at` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店信息主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hotel_info
-- ----------------------------
INSERT INTO `hotel_info` VALUES ('HOTEL-20250610154353-2957', 'AVAHS', 'qweqw', '安顺百灵希尔顿逸林酒店', 'West', '222', '333', '444', '555', 0.00, 0.00, 0.00, 0.00, '2025-06-10 16:21:24', '2025-06-11 08:17:05');

-- ----------------------------
-- Table structure for hotel_usp
-- ----------------------------
DROP TABLE IF EXISTS `hotel_usp`;
CREATE TABLE `hotel_usp`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键唯一标识，建议为UUID',
  `inn_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '酒店Inn Code，用于数据权限控制',
  `row_index` int(11) NOT NULL COMMENT '第几行记录（用于排序）',
  `room` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '客房相关USP描述',
  `food` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '餐饮相关USP描述',
  `meeting` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '会议及宴会相关USP描述',
  `other_service` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '其他服务相关USP描述',
  `created_at` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '记录最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店USP表，每行表示一个USP组合记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for personal_travel_price
-- ----------------------------
DROP TABLE IF EXISTS `personal_travel_price`;
CREATE TABLE `personal_travel_price`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `inn_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `row_index` int(11) NOT NULL,
  `ratio_value` decimal(5, 4) NOT NULL,
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_travel_price`(`inn_code`, `row_index`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for unified_dynamic_region
-- ----------------------------
DROP TABLE IF EXISTS `unified_dynamic_region`;
CREATE TABLE `unified_dynamic_region`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录唯一ID，格式：UDR-yyyymmddhhnnss-xxxx',
  `inn_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '酒店Inn Code',
  `row_index` int(11) NOT NULL COMMENT '实际Excel行号（从41开始）',
  `column_index` int(11) NOT NULL COMMENT '列索引（2=B列，3=C列...12=L列）',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '单元格值',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_cell`(`inn_code`, `row_index`, `column_index`) USING BTREE,
  INDEX `idx_inn_code`(`inn_code`) USING BTREE,
  INDEX `idx_row_range`(`inn_code`, `row_index`) USING BTREE,
  INDEX `idx_updated_at`(`updated_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '统一动态表格区域数据存储' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of unified_dynamic_region
-- ----------------------------
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059270140', 'AVAHS', 43, 7, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059270157', 'AVAHS', 51, 2, '生意板块管理', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059270453', 'AVAHS', 43, 11, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059270456', 'AVAHS', 53, 5, '家庭度假', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059270535', 'AVAHS', 44, 12, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059270562', 'AVAHS', 44, 7, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059271000', 'AVAHS', 52, 3, '选择生意板块', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059271030', 'AVAHS', 52, 4, '市场活动目标', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059271063', 'AVAHS', 49, 3, '4.5-5.5', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059272268', 'AVAHS', 48, 2, '活动/展会名称', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059272439', 'AVAHS', 48, 5, '频率', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059272637', 'AVAHS', 45, 7, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059272793', 'AVAHS', 45, 8, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059272844', 'AVAHS', 53, 2, '餐饮', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059272895', 'AVAHS', 43, 4, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059272957', 'AVAHS', 54, 2, '会议及宴会', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059272981', 'AVAHS', 45, 4, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059273009', 'AVAHS', 55, 2, '其他', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059273019', 'AVAHS', 43, 5, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059273640', 'AVAHS', 44, 9, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059273735', 'AVAHS', 44, 4, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059273820', 'AVAHS', 54, 5, '年轻旅行者', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059274140', 'AVAHS', 43, 12, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059274687', 'AVAHS', 45, 3, 'BLIE', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059275248', 'AVAHS', 44, 10, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059275334', 'AVAHS', 43, 2, 'ALIE', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059275338', 'AVAHS', 49, 2, '春季广交会', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059275751', 'AVAHS', 52, 2, '生意板块', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059275795', 'AVAHS', 43, 3, 'BLIE', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059275891', 'AVAHS', 45, 11, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059275924', 'AVAHS', 45, 2, 'ALIE', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059276226', 'AVAHS', 45, 5, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059276478', 'AVAHS', 45, 6, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059276761', 'AVAHS', 49, 5, '每年123', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059276951', 'AVAHS', 48, 3, '时间', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059277055', 'AVAHS', 42, 2, '价格管理', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059277090', 'AVAHS', 43, 10, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059277607', 'AVAHS', 43, 8, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059277671', 'AVAHS', 44, 11, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059277747', 'AVAHS', 43, 6, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059277904', 'AVAHS', 44, 3, 'BLIE', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059277988', 'AVAHS', 52, 5, '目标市场受众', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059278144', 'AVAHS', 43, 9, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059278246', 'AVAHS', 45, 10, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059278298', 'AVAHS', 45, 9, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059278626', 'AVAHS', 44, 2, 'ALIE', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059278714', 'AVAHS', 44, 6, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059279109', 'AVAHS', 47, 2, '年度重大事件', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059279485', 'AVAHS', 55, 5, '银发族', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059279495', 'AVAHS', 44, 8, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059279619', 'AVAHS', 44, 5, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059279800', 'AVAHS', 48, 4, '影响到的市场板块', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059279860', 'AVAHS', 45, 12, '1', '2025-06-12 10:59:27', '2025-06-12 10:59:27');
INSERT INTO `unified_dynamic_region` VALUES ('UDR202506121059279994', 'AVAHS', 49, 4, 'IBT ', '2025-06-12 10:59:27', '2025-06-12 10:59:27');

SET FOREIGN_KEY_CHECKS = 1;
