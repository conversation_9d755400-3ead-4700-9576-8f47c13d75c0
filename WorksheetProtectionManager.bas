Option Explicit

' =============================================================================
' 工作表保护管理模块 - 希尔顿酒店管理系统
' 统一管理工作表的保护和解除保护操作
' 使用ModGlobal中定义的密码进行保护管理
' =============================================================================

' 临时解除工作表保护并执行操作
Public Function ExecuteWithUnprotectedSheet(ws As Worksheet, operation As String) As Boolean
    On Error GoTo ErrorHandler
    
    ExecuteWithUnprotectedSheet = False
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    
    ' 如果工作表受保护，先解除保护
    If wasProtected Then
        Call LogManager.LogEvent("DEBUG", "临时解除工作表保护: " & ws.Name, ws.Name, "保护管理")
        ws.Unprotect Password:=PWD
    End If
    
    ' 执行操作（这里需要在调用处实现具体操作）
    ExecuteWithUnprotectedSheet = True
    
    ' 恢复保护状态
    If wasProtected Then
        Call LogManager.LogEvent("DEBUG", "恢复工作表保护: " & ws.Name, ws.Name, "保护管理")
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If
    
    Exit Function
    
ErrorHandler:
    ' 如果出错，尝试恢复保护状态
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If
    
    Call LogManager.LogEvent("ERROR", "工作表保护管理异常: " & Err.Description, ws.Name, "保护管理")
    ExecuteWithUnprotectedSheet = False
End Function

' 安全地标记单元格错误（自动处理保护）
Public Sub SafeMarkCellAsError(ws As Worksheet, cellAddress As String)
    On Error GoTo ErrorHandler
    
    Call LogManager.LogEvent("DEBUG", "安全标记错误单元格: " & cellAddress, ws.Name, "样式标记")
    
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    
    ' 临时解除保护
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If
    
    ' 应用错误样式
    With ws.Range(cellAddress)
        .Interior.Color = RGB(255, 200, 200)  ' 浅红色背景
        .Font.Color = RGB(255, 0, 0)          ' 红色字体
        
        ' 设置边框
        .Borders(xlEdgeLeft).Color = RGB(255, 0, 0)
        .Borders(xlEdgeLeft).LineStyle = xlContinuous
        .Borders(xlEdgeLeft).Weight = xlMedium
        
        .Borders(xlEdgeTop).Color = RGB(255, 0, 0)
        .Borders(xlEdgeTop).LineStyle = xlContinuous
        .Borders(xlEdgeTop).Weight = xlMedium
        
        .Borders(xlEdgeBottom).Color = RGB(255, 0, 0)
        .Borders(xlEdgeBottom).LineStyle = xlContinuous
        .Borders(xlEdgeBottom).Weight = xlMedium
        
        .Borders(xlEdgeRight).Color = RGB(255, 0, 0)
        .Borders(xlEdgeRight).LineStyle = xlContinuous
        .Borders(xlEdgeRight).Weight = xlMedium
    End With
    
    ' 恢复保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If
    
    ' 强制刷新显示
    Application.ScreenUpdating = False
    Application.ScreenUpdating = True
    
    Call LogManager.LogEvent("INFO", "成功标记错误单元格: " & cellAddress, ws.Name, "样式标记")
    
    Exit Sub
    
ErrorHandler:
    ' 出错时尝试恢复保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If
    
    Call LogManager.LogEvent("ERROR", "标记错误单元格失败: " & cellAddress & " - " & Err.Description, ws.Name, "样式标记")
End Sub

' 安全地清除单元格错误标记（自动处理保护）
Public Sub SafeClearCellErrorMark(ws As Worksheet, cellAddress As String)
    On Error GoTo ErrorHandler
    
    Call LogManager.LogEvent("DEBUG", "安全清除错误标记: " & cellAddress, ws.Name, "样式清除")
    
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    
    ' 临时解除保护
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If
    
    ' 清除样式
    With ws.Range(cellAddress)
        .Interior.ColorIndex = xlNone
        .Font.ColorIndex = xlAutomatic
        .Borders(xlEdgeLeft).LineStyle = xlNone
        .Borders(xlEdgeTop).LineStyle = xlNone
        .Borders(xlEdgeBottom).LineStyle = xlNone
        .Borders(xlEdgeRight).LineStyle = xlNone
    End With
    
    ' 恢复保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If
    
    ' 强制刷新显示
    Application.ScreenUpdating = False
    Application.ScreenUpdating = True
    
    Call LogManager.LogEvent("INFO", "成功清除错误标记: " & cellAddress, ws.Name, "样式清除")
    
    Exit Sub
    
ErrorHandler:
    ' 出错时尝试恢复保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If
    
    Call LogManager.LogEvent("ERROR", "清除错误标记失败: " & cellAddress & " - " & Err.Description, ws.Name, "样式清除")
End Sub

' 安全地设置单元格值（自动处理保护）
Public Sub SafeSetCellValue(ws As Worksheet, cellAddress As String, value As Variant)
    On Error GoTo ErrorHandler
    
    Dim wasProtected As Boolean
    wasProtected = ws.ProtectContents
    
    ' 临时解除保护
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If
    
    ' 设置值
    ws.Range(cellAddress).Value = value
    
    ' 恢复保护
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If
    
    Exit Sub
    
ErrorHandler:
    ' 出错时尝试恢复保护
    If wasProtected Then
        On Error Resume Next
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
        On Error GoTo 0
    End If
    
    Call LogManager.LogEvent("ERROR", "设置单元格值失败: " & cellAddress & " - " & Err.Description, ws.Name, "数据设置")
End Sub

' 解除工作表保护
Public Function UnprotectWorksheet(ws As Worksheet) As Boolean
    On Error GoTo ErrorHandler
    
    UnprotectWorksheet = False
    
    If ws.ProtectContents Then
        ws.Unprotect Password:=PWD
        Call LogManager.LogEvent("INFO", "工作表保护已解除: " & ws.Name, ws.Name, "保护管理")
    End If
    
    UnprotectWorksheet = True
    Exit Function
    
ErrorHandler:
    Call LogManager.LogEvent("ERROR", "解除工作表保护失败: " & ws.Name & " - " & Err.Description, ws.Name, "保护管理")
    UnprotectWorksheet = False
End Function

' 保护工作表
Public Function ProtectWorksheet(ws As Worksheet) As Boolean
    On Error GoTo ErrorHandler
    
    ProtectWorksheet = False
    
    ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
               AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    
    Call LogManager.LogEvent("INFO", "工作表已保护: " & ws.Name, ws.Name, "保护管理")
    ProtectWorksheet = True
    
    Exit Function
    
ErrorHandler:
    Call LogManager.LogEvent("ERROR", "保护工作表失败: " & ws.Name & " - " & Err.Description, ws.Name, "保护管理")
    ProtectWorksheet = False
End Function

' 检查工作表保护状态
Public Function IsWorksheetProtected(ws As Worksheet) As Boolean
    On Error Resume Next
    IsWorksheetProtected = ws.ProtectContents
End Function

' 批量解除多个工作表保护
Public Sub UnprotectAllWorksheets()
    On Error Resume Next
    
    Dim ws As Worksheet
    For Each ws In ActiveWorkbook.Worksheets
        If ws.ProtectContents Then
            ws.Unprotect Password:=PWD
            Call LogManager.LogEvent("INFO", "批量解除保护: " & ws.Name, ws.Name, "保护管理")
        End If
    Next ws
End Sub

' 批量保护多个工作表
Public Sub ProtectAllWorksheets()
    On Error Resume Next
    
    Dim ws As Worksheet
    For Each ws In ActiveWorkbook.Worksheets
        If Not ws.ProtectContents Then
            ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                       AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
            Call LogManager.LogEvent("INFO", "批量保护: " & ws.Name, ws.Name, "保护管理")
        End If
    Next ws
End Sub

' 测试保护管理功能
Public Sub TestProtectionManager()
    On Error GoTo ErrorHandler
    
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet("1.酒店信息")
    
    If ws Is Nothing Then
        MsgBox "找不到工作表 '1.酒店信息'", vbCritical
        Exit Sub
    End If
    
    Dim msg As String
    msg = "=== 工作表保护管理测试 ===" & vbCrLf & vbCrLf
    
    ' 检查初始状态
    msg = msg & "初始保护状态: " & IIf(IsWorksheetProtected(ws), "受保护", "未保护") & vbCrLf
    
    ' 测试样式标记
    msg = msg & vbCrLf & "测试安全样式标记..." & vbCrLf
    Call SafeMarkCellAsError(ws, "D6")
    msg = msg & "✓ 错误样式已应用到D6" & vbCrLf
    
    ' 等待用户确认
    MsgBox msg & vbCrLf & "请检查D6单元格是否显示红色样式，然后点击确定继续", vbInformation, "测试进行中"
    
    ' 测试清除样式
    Call SafeClearCellErrorMark(ws, "D6")
    msg = msg & "✓ 错误样式已清除" & vbCrLf
    
    ' 检查最终状态
    msg = msg & vbCrLf & "最终保护状态: " & IIf(IsWorksheetProtected(ws), "受保护", "未保护") & vbCrLf
    msg = msg & vbCrLf & "=== 测试完成 ==="
    
    MsgBox msg, vbInformation, "保护管理测试完成"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "测试过程中发生错误: " & Err.Description, vbCritical, "测试错误"
End Sub
