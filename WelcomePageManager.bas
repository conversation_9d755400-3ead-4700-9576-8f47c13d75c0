Option Explicit

' =============================================================================
' 欢迎页管理模块 - 希尔顿酒店管理系统
' 实现登录后自动跳转、数据同步进度显示和流程控制
' 遵循项目代码组织原则：精简代码、复用通用方法、集中Token管理
' 
' 主要功能：
' - 登录成功后自动跳转到欢迎页
' - 显示数据同步进度和状态
' - 同步完成后自动跳转到主页或提供离线模式选择
' - 友好的错误处理和用户交互
' =============================================================================

' 工作表名称常量
Public Const WELCOME_SHEET As String = "欢迎页"
Public Const MAIN_SHEET As String = "主页"

' 欢迎页单元格地址常量
Public Const WELCOME_TITLE_CELL As String = "B2"
Public Const SYNC_STATUS_CELL As String = "B4"
Public Const SYNC_PROGRESS_CELL As String = "B5"
Public Const SYNC_DETAIL_CELL As String = "B6"
Public Const SYNC_TIME_CELL As String = "B7"
Public Const USER_INFO_CELL As String = "B9"

' 欢迎页UI设计常量
Public Const PROGRESS_BAR_CELL As String = "C5"        ' 进度条显示区域
Public Const STATUS_ICON_CELL As String = "A4"        ' 状态图标区域
Public Const WELCOME_LOGO_RANGE As String = "A2:A2"   ' Logo区域


' =============================================================================
' 主要入口函数
' =============================================================================

' 登录成功后的主入口函数
Public Sub OnLoginSuccess()
    On Error GoTo ErrorHandler
    
    Call LogManager.StartNewOperationSession("登录后流程", WELCOME_SHEET)
    
    ' 跳转到欢迎页
    Call NavigateToWelcomePage
    
    ' 显示用户信息
    Call DisplayUserInfo
    
    ' 开始数据同步流程
    Call StartDataSyncProcess
    
    Exit Sub
    
ErrorHandler:
    Call LogManager.LogEvent("ERROR", "登录后流程异常: " & Err.Description, WELCOME_SHEET, "流程控制")
    Call HandleSyncError("登录后流程异常: " & Err.Description)
End Sub

' 开始数据同步流程
Public Sub StartDataSyncProcess()
    On Error GoTo ErrorHandler

    Dim startTime As Double
    startTime = Timer

    ' 禁用屏幕更新以提升性能
    Dim originalScreenUpdating As Boolean
    originalScreenUpdating = Application.ScreenUpdating
    Application.ScreenUpdating = False

    ' 禁用事件处理，防止在数据同步期间意外触发SegmentForm等窗体
    Dim originalEventsState As Boolean
    originalEventsState = Application.EnableEvents
    Application.EnableEvents = False

    ' 设置系统初始化状态，防止SegmentForm在数据同步期间显示
    Call SegmentFormManager.SetSystemInitializingState(True)

    Call LogManager.LogEvent("INFO", "数据同步开始，已禁用事件处理和窗体显示", WELCOME_SHEET, "事件管理")
    
    ' 步骤1: 初始化同步状态
    Call UpdateSyncStatus(SYNC_STARTING, "正在初始化数据同步...", 0)
    
    ' 步骤2: 验证数据库连接
    Call UpdateSyncStatus(SYNC_IN_PROGRESS, "正在验证数据库连接...", 20)
    If Not DabaseCore.TestConnection() Then
        Call HandleSyncError("数据库连接失败")
        GoTo CleanupAndExit
    End If
    
    ' 步骤3: 加载酒店信息
    Call UpdateSyncStatus(SYNC_IN_PROGRESS, "正在加载酒店信息...", 40)
    Dim hotelLoadSuccess As Boolean
    hotelLoadSuccess = True

    ' 尝试加载酒店信息，如果失败则记录但继续
    On Error Resume Next
    Call HotelInfoManager.LoadHotelInfoFromDB
    If Err.Number <> 0 Then
        hotelLoadSuccess = False
        Call LogManager.LogEvent("WARNING", "酒店信息加载失败: " & Err.Description, WELCOME_SHEET, "数据同步")
        Err.Clear
    End If
    On Error GoTo ErrorHandler

    ' 步骤4: 验证数据完整性
    Call UpdateSyncStatus(SYNC_IN_PROGRESS, "正在验证数据完整性...", 60)
    If hotelLoadSuccess Then
        ' 临时启用屏幕更新以显示验证结果
        Application.ScreenUpdating = True
        DoEvents
        Application.ScreenUpdating = False
    End If

    ' 步骤5: 初始化业务背景工作表
    Call UpdateSyncStatus(SYNC_IN_PROGRESS, "正在初始化业务背景工作表...", 65)
    On Error Resume Next
    Call BusinessForecastManager.InitializeBusinessForecastSheet
    If Err.Number <> 0 Then
        Call LogManager.LogEvent("WARNING", "业务背景工作表初始化失败: " & Err.Description, WELCOME_SHEET, "数据同步")
        Err.Clear
    End If
    On Error GoTo ErrorHandler

    ' 步骤6: 同步其他模块数据（预留扩展）
    Call UpdateSyncStatus(SYNC_IN_PROGRESS, "正在同步其他数据模块...", 70)
    ' 这里可以添加其他模块的数据同步

    ' 步骤7: 完成同步
    Call UpdateSyncStatus(SYNC_IN_PROGRESS, "正在完成数据同步...", 80)
    
    Dim elapsedTime As Double
    elapsedTime = Timer - startTime

    ' 步骤8: 同步成功
    Dim syncMessage As String
    If hotelLoadSuccess Then
        syncMessage = "数据同步完成，酒店信息已加载"
    Else
        syncMessage = "数据同步完成，部分模块加载失败"
    End If

    Call UpdateSyncStatus(SYNC_SUCCESS, syncMessage, 100)
    Call UpdateSyncTime("同步耗时: " & Format(elapsedTime, "0.0") & "秒")

    ' 恢复屏幕更新和事件处理
    Application.ScreenUpdating = originalScreenUpdating
    Application.EnableEvents = originalEventsState

    ' 清除系统初始化状态，允许SegmentForm正常显示
    Call SegmentFormManager.SetSystemInitializingState(False)

    Call LogManager.LogEvent("INFO", "数据同步完成，已恢复事件处理和窗体显示", WELCOME_SHEET, "事件管理")

    ' 延迟2秒后跳转到主页
    Application.Wait Now + TimeValue("00:00:02")
    Call NavigateToMainPage

    Call LogManager.LogEvent("INFO", "数据同步完成，耗时: " & Format(elapsedTime, "0.0") & "秒", WELCOME_SHEET, "数据同步")
    Call LogManager.EndCurrentOperationSession("登录后流程", "SUCCESS", WELCOME_SHEET)

    Exit Sub

CleanupAndExit:
    Application.ScreenUpdating = originalScreenUpdating
    Application.EnableEvents = originalEventsState
    Call SegmentFormManager.SetSystemInitializingState(False)
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = originalScreenUpdating
    Application.EnableEvents = originalEventsState
    Call SegmentFormManager.SetSystemInitializingState(False)
    Call LogManager.LogEvent("ERROR", "数据同步异常: " & Err.Description, WELCOME_SHEET, "数据同步")
    Call HandleSyncError("数据同步过程中发生异常: " & Err.Description)
End Sub

' =============================================================================
' 页面导航函数
' =============================================================================

' 跳转到欢迎页
Public Sub NavigateToWelcomePage()
    On Error Resume Next
    
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(WELCOME_SHEET)
    
    If Not ws Is Nothing Then
        ws.Activate
        ws.Range("A1").Select
        Call InitializeWelcomePage(ws)
    End If
End Sub

' 跳转到主页（使用智能查找逻辑）
Public Sub NavigateToMainPage()
    On Error Resume Next

    Dim ws As Worksheet
    Dim homePageNames As Variant
    Dim i As Integer
    Dim homePageFound As Boolean

    ' 定义可能的主页名称（与SheetManager保持一致）
    homePageNames = Array("主页", "首页", "Home", "HomePage", "Main", "主界面")
    homePageFound = False

    ' 遍历所有工作表，查找主页
    For Each ws In ThisWorkbook.Worksheets
        For i = LBound(homePageNames) To UBound(homePageNames)
            If InStr(1, ws.Name, homePageNames(i), vbTextCompare) > 0 Then
                ' 找到主页，激活它
                ws.Activate
                ws.Range("A1").Select
                homePageFound = True
                Call DataHelper.ShowSyncStatus("欢迎使用希尔顿酒店管理系统")
                Call LogManager.LogEvent("INFO", "已跳转到主页工作表: " & ws.Name, WELCOME_SHEET, "页面导航")
                Exit Sub
            End If
        Next i
    Next ws

    ' 如果没有找到主页，尝试使用常量定义的工作表名
    If Not homePageFound Then
        Set ws = DataHelper.GetWorksheet(MAIN_SHEET)
        If Not ws Is Nothing Then
            ws.Activate
            ws.Range("A1").Select
            Call DataHelper.ShowSyncStatus("欢迎使用希尔顿酒店管理系统")
            Call LogManager.LogEvent("INFO", "已跳转到主页工作表: " & ws.Name, WELCOME_SHEET, "页面导航")
        Else
            ' 如果仍然找不到，激活第一个可见工作表
            For Each ws In ThisWorkbook.Worksheets
                If ws.Visible = xlSheetVisible Then
                    ws.Activate
                    ws.Range("A1").Select
                    Call DataHelper.ShowSyncStatus("欢迎使用希尔顿酒店管理系统")
                    Call LogManager.LogEvent("WARNING", "未找到主页，已激活工作表: " & ws.Name, WELCOME_SHEET, "页面导航")
                    Exit Sub
                End If
            Next ws
        End If
    End If
End Sub

' =============================================================================
' 欢迎页界面管理函数
' =============================================================================

' 初始化欢迎页界面（增强版UI设计）
Private Sub InitializeWelcomePage(ByRef ws As Worksheet)
    On Error Resume Next

    ' 禁用屏幕更新以提升性能
    Application.ScreenUpdating = False

    ' 清理和准备工作区域
    Call PrepareWelcomePageLayout(ws)

    ' 设置主标题 - 专业的企业级设计
    Call SetupWelcomeTitle(ws)

    ' 初始化状态显示区域 - 现代化卡片式设计
    Call InitializeStatusDisplayArea(ws)

    ' 设置进度显示区域 - 可视化进度条
    Call InitializeProgressDisplayArea(ws)

    ' 设置用户信息区域 - 优雅的信息展示
    Call InitializeUserInfoArea(ws)

    ' 应用整体页面样式
    Call ApplyWelcomePageStyling(ws)

    ' 恢复屏幕更新
    Application.ScreenUpdating = True
End Sub

' 准备欢迎页布局
Private Sub PrepareWelcomePageLayout(ByRef ws As Worksheet)
    On Error Resume Next

    ' 设置列宽以优化显示效果
    ws.Columns("A:A").ColumnWidth = 4      ' 图标列
    ws.Columns("B:B").ColumnWidth = 35     ' 主要内容列
    ws.Columns("C:C").ColumnWidth = 25     ' 进度条列
    ws.Columns("D:D").ColumnWidth = 15     ' 辅助列

    ' 设置行高
    ws.Rows("2:2").RowHeight = 30          ' 标题行
    ws.Rows("4:7").RowHeight = 25          ' 状态信息行
    ws.Rows("9:9").RowHeight = 20          ' 用户信息行

    ' 清除可能存在的旧格式
    ws.Range("A1:E15").ClearFormats
End Sub

' 设置欢迎标题
Private Sub SetupWelcomeTitle(ByRef ws As Worksheet)
    On Error Resume Next

    ' 设置标题内容
    ws.Range(WELCOME_TITLE_CELL).Value = "欢迎使用希尔顿酒店管理系统"

    ' 合并标题单元格以获得更好的视觉效果
    ws.Range("A2:D2").Merge

    ' 设置标题样式 - 企业级专业设计
    With ws.Range("A2:D2")
        .Font.Name = "微软雅黑"
        .Font.Size = 18
        .Font.Bold = True
        .Font.Color = RGB(0, 51, 102)           ' 深蓝色 - 希尔顿品牌色调
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Interior.Color = RGB(240, 248, 255)    ' 淡蓝色背景
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(70, 130, 180)      ' 钢蓝色边框
        .Borders.Weight = xlMedium
    End With
End Sub

' 初始化状态显示区域
Private Sub InitializeStatusDisplayArea(ByRef ws As Worksheet)
    On Error Resume Next

    ' 设置状态图标
    ws.Range(STATUS_ICON_CELL).Value = "●"
    ws.Range(STATUS_ICON_CELL).Font.Size = 14
    ws.Range(STATUS_ICON_CELL).Font.Color = RGB(128, 128, 128)  ' 灰色待机状态
    ws.Range(STATUS_ICON_CELL).HorizontalAlignment = xlCenter

    ' 初始化状态文本
    ws.Range(SYNC_STATUS_CELL).Value = "系统状态: 准备就绪"
    ws.Range(SYNC_DETAIL_CELL).Value = "操作详情: 等待开始..."
    ws.Range(SYNC_TIME_CELL).Value = "同步时间: --"

    ' 设置状态区域样式
    Call ApplyStatusAreaStyling(ws)
End Sub

' 应用状态区域样式
Private Sub ApplyStatusAreaStyling(ByRef ws As Worksheet)
    On Error Resume Next

    ' 状态单元格样式
    With ws.Range(SYNC_STATUS_CELL)
        .Font.Name = "微软雅黑"
        .Font.Size = 11
        .Font.Bold = True
        .Interior.Color = RGB(248, 248, 248)    ' 浅灰色背景
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(200, 200, 200)
        .Borders.Weight = xlThin
    End With

    ' 详情单元格样式
    With ws.Range(SYNC_DETAIL_CELL)
        .Font.Name = "微软雅黑"
        .Font.Size = 10
        .Interior.Color = RGB(252, 252, 252)    ' 极浅灰色背景
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(220, 220, 220)
        .Borders.Weight = xlThin
    End With

    ' 时间单元格样式
    With ws.Range(SYNC_TIME_CELL)
        .Font.Name = "微软雅黑"
        .Font.Size = 9
        .Font.Color = RGB(100, 100, 100)        ' 深灰色文字
        .Interior.Color = RGB(250, 250, 250)    ' 浅灰色背景
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(230, 230, 230)
        .Borders.Weight = xlThin
    End With
End Sub

' 初始化进度显示区域
Private Sub InitializeProgressDisplayArea(ByRef ws As Worksheet)
    On Error Resume Next

    ' 设置进度文本
    ws.Range(SYNC_PROGRESS_CELL).Value = "同步进度: 0%"

    ' 设置进度条背景
    ws.Range(PROGRESS_BAR_CELL).Value = "░░░░░░░░░░"  ' 使用Unicode字符创建进度条背景

    ' 设置进度区域样式
    With ws.Range(SYNC_PROGRESS_CELL)
        .Font.Name = "微软雅黑"
        .Font.Size = 10
        .Font.Bold = True
        .Interior.Color = RGB(245, 245, 245)
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(200, 200, 200)
        .Borders.Weight = xlThin
    End With

    ' 设置进度条样式
    With ws.Range(PROGRESS_BAR_CELL)
        .Font.Name = "Consolas"
        .Font.Size = 12
        .Font.Color = RGB(200, 200, 200)
        .HorizontalAlignment = xlLeft
        .Interior.Color = RGB(250, 250, 250)
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(220, 220, 220)
        .Borders.Weight = xlThin
    End With
End Sub

' 初始化用户信息区域
Private Sub InitializeUserInfoArea(ByRef ws As Worksheet)
    On Error Resume Next

    ' 设置用户信息初始值
    ws.Range(USER_INFO_CELL).Value = "登录时间: " & Format(Now, "yyyy-mm-dd hh:nn:ss")

    ' 设置用户信息区域样式
    With ws.Range(USER_INFO_CELL)
        .Font.Name = "微软雅黑"
        .Font.Size = 9
        .Font.Color = RGB(80, 80, 80)
        .Interior.Color = RGB(248, 252, 255)    ' 极淡蓝色背景
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(180, 200, 220)
        .Borders.Weight = xlThin
        .HorizontalAlignment = xlLeft
    End With
End Sub

' 应用整体页面样式
Private Sub ApplyWelcomePageStyling(ByRef ws As Worksheet)
    On Error Resume Next

    ' 设置整体页面背景
    ws.Range("A1:E15").Interior.Color = RGB(255, 255, 255)  ' 白色背景

    ' 添加页面边框装饰
    With ws.Range("A1:D10")
        .Borders(xlEdgeTop).LineStyle = xlContinuous
        .Borders(xlEdgeTop).Color = RGB(70, 130, 180)
        .Borders(xlEdgeTop).Weight = xlThick

        .Borders(xlEdgeBottom).LineStyle = xlContinuous
        .Borders(xlEdgeBottom).Color = RGB(70, 130, 180)
        .Borders(xlEdgeBottom).Weight = xlMedium

        .Borders(xlEdgeLeft).LineStyle = xlContinuous
        .Borders(xlEdgeLeft).Color = RGB(70, 130, 180)
        .Borders(xlEdgeLeft).Weight = xlMedium

        .Borders(xlEdgeRight).LineStyle = xlContinuous
        .Borders(xlEdgeRight).Color = RGB(70, 130, 180)
        .Borders(xlEdgeRight).Weight = xlMedium
    End With
End Sub

' 更新同步状态显示（增强版UI设计）- 修复ByRef参数问题
Private Sub UpdateSyncStatus(ByVal status As SyncStatus, ByVal detail As String, ByVal progress As Integer)
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(WELCOME_SHEET)

    If ws Is Nothing Then Exit Sub

    ' 更新状态图标和颜色
    Call UpdateStatusIcon(ws, status)

    ' 更新状态文本和样式
    Call UpdateStatusText(ws, status)

    ' 更新可视化进度条
    Call UpdateProgressBar(ws, progress)

    ' 更新详情信息
    Call UpdateDetailInfo(ws, detail)

    ' 更新状态栏
    Call DataHelper.ShowSyncStatus(detail)

    ' 强制刷新显示
    DoEvents
End Sub

' 更新状态图标
Private Sub UpdateStatusIcon(ByRef ws As Worksheet, ByVal status As SyncStatus)
    On Error Resume Next

    Select Case status
        Case SYNC_STARTING
            ws.Range(STATUS_ICON_CELL).Value = "◐"
            ws.Range(STATUS_ICON_CELL).Font.Color = RGB(255, 165, 0)  ' 橙色
        Case SYNC_IN_PROGRESS
            ws.Range(STATUS_ICON_CELL).Value = "◑"
            ws.Range(STATUS_ICON_CELL).Font.Color = RGB(30, 144, 255) ' 蓝色
        Case SYNC_SUCCESS
            ws.Range(STATUS_ICON_CELL).Value = "●"
            ws.Range(STATUS_ICON_CELL).Font.Color = RGB(34, 139, 34)  ' 绿色
        Case SYNC_FAILED
            ws.Range(STATUS_ICON_CELL).Value = "●"
            ws.Range(STATUS_ICON_CELL).Font.Color = RGB(220, 20, 60)  ' 红色
    End Select
End Sub

' 更新状态文本
Private Sub UpdateStatusText(ByRef ws As Worksheet, ByVal status As SyncStatus)
    On Error Resume Next

    Select Case status
        Case SYNC_STARTING
            ws.Range(SYNC_STATUS_CELL).Value = "系统状态: 正在启动..."
            ws.Range(SYNC_STATUS_CELL).Interior.Color = RGB(255, 248, 220)  ' 浅橙色背景
            ws.Range(SYNC_STATUS_CELL).Font.Color = RGB(255, 140, 0)        ' 橙色文字
        Case SYNC_IN_PROGRESS
            ws.Range(SYNC_STATUS_CELL).Value = "系统状态: 同步中..."
            ws.Range(SYNC_STATUS_CELL).Interior.Color = RGB(240, 248, 255)  ' 浅蓝色背景
            ws.Range(SYNC_STATUS_CELL).Font.Color = RGB(30, 144, 255)       ' 蓝色文字
        Case SYNC_SUCCESS
            ws.Range(SYNC_STATUS_CELL).Value = "系统状态: 同步成功 ✓"
            ws.Range(SYNC_STATUS_CELL).Interior.Color = RGB(240, 255, 240)  ' 浅绿色背景
            ws.Range(SYNC_STATUS_CELL).Font.Color = RGB(34, 139, 34)        ' 绿色文字
        Case SYNC_FAILED
            ws.Range(SYNC_STATUS_CELL).Value = "系统状态: 同步失败 ✗"
            ws.Range(SYNC_STATUS_CELL).Interior.Color = RGB(255, 240, 245)  ' 浅红色背景
            ws.Range(SYNC_STATUS_CELL).Font.Color = RGB(220, 20, 60)        ' 红色文字
    End Select
End Sub

' 更新可视化进度条
Private Sub UpdateProgressBar(ByRef ws As Worksheet, ByVal progress As Integer)
    On Error Resume Next

    ' 更新进度文本
    ws.Range(SYNC_PROGRESS_CELL).Value = "同步进度: " & progress & "%"

    ' 计算进度条显示
    Dim progressChars As Integer
    Dim totalChars As Integer
    totalChars = 10
    progressChars = Int((progress / 100) * totalChars)

    ' 构建可视化进度条
    Dim progressBar As String
    Dim i As Integer

    progressBar = ""
    For i = 1 To totalChars
        If i <= progressChars Then
            progressBar = progressBar & "█"  ' 已完成部分
        Else
            progressBar = progressBar & "░"  ' 未完成部分
        End If
    Next i

    ' 更新进度条显示
    ws.Range(PROGRESS_BAR_CELL).Value = progressBar

    ' 根据进度设置颜色
    If progress < 30 Then
        ws.Range(PROGRESS_BAR_CELL).Font.Color = RGB(255, 140, 0)   ' 橙色
        ws.Range(SYNC_PROGRESS_CELL).Font.Color = RGB(255, 140, 0)
    ElseIf progress < 70 Then
        ws.Range(PROGRESS_BAR_CELL).Font.Color = RGB(30, 144, 255)  ' 蓝色
        ws.Range(SYNC_PROGRESS_CELL).Font.Color = RGB(30, 144, 255)
    ElseIf progress < 100 Then
        ws.Range(PROGRESS_BAR_CELL).Font.Color = RGB(70, 130, 180)  ' 钢蓝色
        ws.Range(SYNC_PROGRESS_CELL).Font.Color = RGB(70, 130, 180)
    Else
        ws.Range(PROGRESS_BAR_CELL).Font.Color = RGB(34, 139, 34)   ' 绿色
        ws.Range(SYNC_PROGRESS_CELL).Font.Color = RGB(34, 139, 34)
    End If
End Sub

' 更新详情信息
Private Sub UpdateDetailInfo(ByRef ws As Worksheet, ByVal detail As String)
    On Error Resume Next

    ' 更新详情文本
    ws.Range(SYNC_DETAIL_CELL).Value = "操作详情: " & detail

    ' 根据详情内容设置样式
    If InStr(detail, "失败") > 0 Or InStr(detail, "错误") > 0 Then
        ws.Range(SYNC_DETAIL_CELL).Interior.Color = RGB(255, 245, 245)  ' 浅红色背景
        ws.Range(SYNC_DETAIL_CELL).Font.Color = RGB(220, 20, 60)        ' 红色文字
    ElseIf InStr(detail, "成功") > 0 Or InStr(detail, "完成") > 0 Then
        ws.Range(SYNC_DETAIL_CELL).Interior.Color = RGB(245, 255, 245)  ' 浅绿色背景
        ws.Range(SYNC_DETAIL_CELL).Font.Color = RGB(34, 139, 34)        ' 绿色文字
    Else
        ws.Range(SYNC_DETAIL_CELL).Interior.Color = RGB(248, 248, 248)  ' 浅灰色背景
        ws.Range(SYNC_DETAIL_CELL).Font.Color = RGB(80, 80, 80)         ' 深灰色文字
    End If
End Sub

' 更新同步时间显示（增强版）
Private Sub UpdateSyncTime(ByVal timeInfo As String)
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(WELCOME_SHEET)

    If Not ws Is Nothing Then
        ws.Range(SYNC_TIME_CELL).Value = "同步时间: " & timeInfo

        ' 设置时间显示样式
        With ws.Range(SYNC_TIME_CELL)
            .Font.Color = RGB(100, 100, 100)        ' 深灰色文字
            .Interior.Color = RGB(250, 250, 250)    ' 浅灰色背景
            .Font.Italic = True                     ' 斜体显示时间
        End With
    End If
End Sub

' 显示用户信息（增强版UI设计）
Private Sub DisplayUserInfo()
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(WELCOME_SHEET)

    If ws Is Nothing Then Exit Sub

    Dim userInfo As String
    Dim innCode As String
    Dim userName As String

    ' 获取用户信息
    innCode = TokenManager.GetCurrentHotelInncode()
    userName = TokenManager.GetCurrentUser()

    ' 构建用户信息字符串
    If Trim(innCode) <> "" And Trim(userName) <> "" Then
        userInfo = innCode & " | 👤 " & userName & " |  " & Format(Now, "yyyy-mm-dd hh:nn:ss")
    ElseIf Trim(innCode) <> "" Then
        userInfo = innCode & " |  " & Format(Now, "yyyy-mm-dd hh:nn:ss")
    Else
        userInfo = " 登录时间: " & Format(Now, "yyyy-mm-dd hh:nn:ss")
    End If

    ' 设置用户信息
    ws.Range(USER_INFO_CELL).Value = userInfo

    ' 应用用户信息区域的增强样式
    Call ApplyUserInfoStyling(ws)
End Sub

' 应用用户信息区域样式
Private Sub ApplyUserInfoStyling(ByRef ws As Worksheet)
    On Error Resume Next

    ' 扩展用户信息显示区域
    ws.Range("A9:D9").Merge

    With ws.Range("A9:D9")
        .Font.Name = "微软雅黑"
        .Font.Size = 9
        .Font.Color = RGB(70, 70, 70)           ' 深灰色文字
        .Interior.Color = RGB(248, 252, 255)    ' 极淡蓝色背景
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(180, 200, 220)     ' 淡蓝色边框
        .Borders.Weight = xlThin
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With
End Sub

' 创建UI测试函数 - 用于测试新的界面设计
Public Sub TestEnhancedWelcomePageUI()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(WELCOME_SHEET)

    If ws Is Nothing Then
        MsgBox "找不到欢迎页工作表，请先创建名为'" & WELCOME_SHEET & "'的工作表", vbCritical, "测试失败"
        Exit Sub
    End If

    ' 初始化增强版UI
    Call InitializeWelcomePage(ws)

    ' 显示用户信息
    Call DisplayUserInfo

    ' 模拟同步过程以展示UI效果
    Call DemoSyncProcess

    MsgBox "增强版欢迎页UI测试完成！" & vbCrLf & vbCrLf & _
           "新增功能：" & vbCrLf & _
           "• 专业的企业级标题设计" & vbCrLf & _
           "• 彩色状态图标指示" & vbCrLf & _
           "• 可视化进度条显示" & vbCrLf & _
           "• 智能颜色编码系统" & vbCrLf & _
           "• 现代化卡片式布局" & vbCrLf & _
           "• 优化的字体和间距设计", vbInformation, "UI增强测试"

    Exit Sub

ErrorHandler:
    MsgBox "UI测试过程中发生异常: " & Err.Description, vbCritical, "测试异常"
End Sub

' 演示同步过程（用于UI测试）- 修复ByRef参数问题
Private Sub DemoSyncProcess()
    On Error Resume Next

    Dim i As Integer
    Dim demoSteps As Variant

    ' 定义演示步骤
    demoSteps = Array( _
        Array(SYNC_STARTING, "正在初始化系统...", 10), _
        Array(SYNC_IN_PROGRESS, "正在连接数据库...", 30), _
        Array(SYNC_IN_PROGRESS, "正在加载酒店信息...", 50), _
        Array(SYNC_IN_PROGRESS, "正在验证数据完整性...", 70), _
        Array(SYNC_IN_PROGRESS, "正在完成同步...", 90), _
        Array(SYNC_SUCCESS, "数据同步完成，系统准备就绪", 100) _
    )

    ' 逐步演示UI效果 - 使用变量避免ByRef参数问题
    For i = LBound(demoSteps) To UBound(demoSteps)
        ' 将数组元素赋值给变量，确保ByRef参数兼容性
        Dim currentStatus As SyncStatus
        Dim currentDetail As String
        Dim currentProgress As Integer

        currentStatus = demoSteps(i)(0)
        currentDetail = demoSteps(i)(1)
        currentProgress = demoSteps(i)(2)

        Call UpdateSyncStatus(currentStatus, currentDetail, currentProgress)
        Application.Wait Now + TimeValue("00:00:01")  ' 等待1秒
    Next i

    ' 更新时间信息
    Call UpdateSyncTime("演示耗时: 6.0秒")
End Sub

' =============================================================================
' 错误处理和离线模式函数
' =============================================================================

' 处理同步错误
Private Sub HandleSyncError(errorMessage As String)
    On Error Resume Next
    
    ' 更新错误状态显示
    Call UpdateSyncStatus(SYNC_FAILED, errorMessage, 0)
    
    ' 显示错误处理选项
    Call ShowErrorHandlingOptions(errorMessage)
End Sub

' 显示错误处理选项
Private Sub ShowErrorHandlingOptions(errorMessage As String)
    On Error GoTo ErrorHandler
    
    Dim response As VbMsgBoxResult
    Dim msg As String
    
    msg = "数据同步失败：" & vbCrLf & vbCrLf
    msg = msg & errorMessage & vbCrLf & vbCrLf
    msg = msg & "您可以选择以下操作：" & vbCrLf
    msg = msg & "• 是(Y) - 重试数据同步" & vbCrLf
    msg = msg & "• 否(N) - 进入离线模式" & vbCrLf
    msg = msg & "• 取消 - 查看详细错误信息"
    
    response = MsgBox(msg, vbYesNoCancel + vbQuestion, "数据同步失败")
    
    Select Case response
        Case vbYes
            ' 重试同步
            Call StartDataSyncProcess
        Case vbNo
            ' 进入离线模式
            Call EnterOfflineMode
        Case vbCancel
            ' 显示详细错误信息
            Call ShowDetailedErrorInfo(errorMessage)
    End Select
    
    Exit Sub
    
ErrorHandler:
    Call LogManager.LogEvent("ERROR", "错误处理选项显示异常: " & Err.Description, WELCOME_SHEET, "错误处理")
End Sub

' 进入离线模式
Private Sub EnterOfflineMode()
    On Error Resume Next
    
    ' 解锁所有可见工作表
    Call UnlockAllWorksheets
    
    ' 跳转到主页
    Call NavigateToMainPage
    
    ' 显示离线模式提示
    Call DataHelper.ShowSyncStatus("离线模式 - 数据将在本地保存，请稍后手动同步")
    
    Call LogManager.LogEvent("INFO", "用户选择进入离线模式", WELCOME_SHEET, "离线模式")
End Sub

' 解锁所有工作表
Private Sub UnlockAllWorksheets()
    On Error Resume Next
    
    Dim ws As Worksheet
    For Each ws In ThisWorkbook.Worksheets
        If ws.Visible = xlSheetVisible Then
            ws.Unprotect Password:=PWD
        End If
    Next ws
    
    Call LogManager.LogEvent("INFO", "已解锁所有可见工作表以支持离线模式", WELCOME_SHEET, "离线模式")
End Sub



' 显示详细错误信息
Private Sub ShowDetailedErrorInfo(errorMessage As String)
    On Error Resume Next
    
    Dim msg As String
    msg = "详细错误信息：" & vbCrLf & vbCrLf
    msg = msg & errorMessage & vbCrLf & vbCrLf
    msg = msg & "建议解决方案：" & vbCrLf
    msg = msg & "1. 检查网络连接是否正常" & vbCrLf
    msg = msg & "2. 确认数据库服务器是否可访问" & vbCrLf
    msg = msg & "3. 验证登录凭据是否有效" & vbCrLf
    msg = msg & "4. 联系系统管理员获取技术支持"
    
    MsgBox msg, vbInformation, "详细错误信息"
    
    ' 返回错误处理选项
    Call ShowErrorHandlingOptions(errorMessage)
End Sub

