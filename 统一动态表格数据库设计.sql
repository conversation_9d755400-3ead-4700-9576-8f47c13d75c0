-- =============================================================================
-- 统一动态表格区域数据库表结构设计
-- 希尔顿酒店管理系统 - 业务背景工作表动态区域
-- =============================================================================

-- 主表：统一动态区域数据存储
CREATE TABLE unified_dynamic_region (
    id VARCHAR(50) PRIMARY KEY COMMENT '记录唯一ID，格式：UDR-yyyymmddhhnnss-xxxx',
    inn_code VARCHAR(50) NOT NULL COMMENT '酒店Inn Code',
    row_index INT NOT NULL COMMENT '实际Excel行号（从41开始）',
    column_index INT NOT NULL COMMENT '列索引（2=B列，3=C列...12=L列）',
    value TEXT COMMENT '单元格值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 唯一约束：每个酒店的每个单元格只能有一条记录
    UNIQUE KEY unique_cell (inn_code, row_index, column_index),
    
    -- 索引优化
    INDEX idx_inn_code (inn_code),
    INDEX idx_row_range (inn_code, row_index),
    INDEX idx_updated_at (updated_at)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一动态表格区域数据存储';

-- =============================================================================
-- 数据存储策略说明
-- =============================================================================

/*
1. 存储范围：
   - 起始行：41（标题行）
   - 列范围：B-L（column_index: 2-12）
   - 动态结束行：根据实际数据内容确定

2. 区域划分（通过row_index区分）：
   - 价格管理区域：通常从42行开始
   - 年度重大事件区域：通过扫描B列"年度重大事件"标识确定起始行
   - 生意板块区域：通过扫描B列"生意板块"标识确定起始行

3. 数据特点：
   - 稀疏存储：只存储非空单元格
   - 动态边界：根据实际数据内容确定存储范围
   - 统一格式：所有区域使用相同的存储结构

4. 性能优化：
   - 批量操作：使用数组一次性读取/写入
   - 索引优化：针对查询模式设计索引
   - 事务处理：保证数据一致性
*/

-- =============================================================================
-- 示例数据
-- =============================================================================

-- 价格管理区域示例数据
INSERT INTO unified_dynamic_region (id, inn_code, row_index, column_index, value) VALUES
('UDR-20241201120000-0001', 'HILTON001', 41, 2, '价格管理'),
('UDR-20241201120000-0002', 'HILTON001', 42, 2, '价格类型1'),
('UDR-20241201120000-0003', 'HILTON001', 42, 3, '价格类型1'),
('UDR-20241201120000-0004', 'HILTON001', 42, 4, '100'),
('UDR-20241201120000-0005', 'HILTON001', 43, 4, '200');

-- 年度重大事件区域示例数据
INSERT INTO unified_dynamic_region (id, inn_code, row_index, column_index, value) VALUES
('UDR-20241201120000-0006', 'HILTON001', 49, 2, '年度重大事件'),
('UDR-20241201120000-0007', 'HILTON001', 50, 2, '春节'),
('UDR-20241201120000-0008', 'HILTON001', 50, 3, '2025-02-01'),
('UDR-20241201120000-0009', 'HILTON001', 51, 2, '国庆节'),
('UDR-20241201120000-0010', 'HILTON001', 51, 3, '2025-10-01');

-- 生意板块区域示例数据
INSERT INTO unified_dynamic_region (id, inn_code, row_index, column_index, value) VALUES
('UDR-20241201120000-0011', 'HILTON001', 55, 2, '生意板块'),
('UDR-20241201120000-0012', 'HILTON001', 56, 2, '商务客户'),
('UDR-20241201120000-0013', 'HILTON001', 56, 3, '60%'),
('UDR-20241201120000-0014', 'HILTON001', 57, 2, '休闲客户'),
('UDR-20241201120000-0015', 'HILTON001', 57, 3, '40%');

-- =============================================================================
-- 常用查询语句
-- =============================================================================

-- 1. 查询指定酒店的所有动态区域数据
SELECT row_index, column_index, value 
FROM unified_dynamic_region 
WHERE inn_code = 'HILTON001' 
ORDER BY row_index, column_index;

-- 2. 查询指定酒店指定行范围的数据
SELECT row_index, column_index, value 
FROM unified_dynamic_region 
WHERE inn_code = 'HILTON001' 
  AND row_index BETWEEN 42 AND 48
ORDER BY row_index, column_index;

-- 3. 删除指定酒店的所有动态区域数据
DELETE FROM unified_dynamic_region 
WHERE inn_code = 'HILTON001';

-- 4. 统计指定酒店的数据量
SELECT COUNT(*) as total_records 
FROM unified_dynamic_region 
WHERE inn_code = 'HILTON001';

-- 5. 查询最近更新的数据
SELECT inn_code, row_index, column_index, value, updated_at
FROM unified_dynamic_region 
WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
ORDER BY updated_at DESC;

-- =============================================================================
-- 数据维护脚本
-- =============================================================================

-- 清理超过30天的历史数据（如果需要）
-- DELETE FROM unified_dynamic_region 
-- WHERE updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 重建索引（维护时使用）
-- ALTER TABLE unified_dynamic_region DROP INDEX idx_inn_code;
-- ALTER TABLE unified_dynamic_region ADD INDEX idx_inn_code (inn_code);

-- 表结构优化检查
-- ANALYZE TABLE unified_dynamic_region;
-- OPTIMIZE TABLE unified_dynamic_region;
