Option Explicit

' =============================================================================
' 数据同步核心模块 - 希尔顿酒店管理系统
' 负责协调各个同步模块的执行
' =============================================================================

' 静默数据同步主函数
Public Sub SyncAllDataSilently()
    On Error GoTo ErrorHandler

    ' 开始新的同步操作会话
    Call LogManager.StartNewOperationSession("SYNC_ALL", "DataSyncCore")

    ' 检查数据库连接
    If Not TestConnection() Then
        Call LogManager.LogSyncEvent("ERROR", "数据库连接失败，跳过同步", "SYNC_ALL", "DataSyncCore")
        Call LogManager.EndCurrentOperationSession("SYNC_ALL", "FAILED", "DataSyncCore")
        Exit Sub
    End If

    ' 显示状态提示
    Application.StatusBar = "正在同步数据..."
    DoEvents

    ' 同步主要模块
    ' 1. 酒店信息同步
    Call HotelInfoManager.SaveHotelInfoToDB

    ' 2. 业务背景预测同步
    Call BusinessForecastManager.SaveForecastDataToDB

    ' 其他模块待添加


    ' 清除状态栏并显示完成状态
    Application.StatusBar = False
    Call DataHelper.ShowSyncStatus("所有数据同步完成 " & Format(Now, "hh:nn:ss"))
    Call LogManager.LogSyncEvent("INFO", "所有数据同步完成", "SYNC_ALL", "DataSyncCore")
    Call LogManager.EndCurrentOperationSession("SYNC_ALL", "SUCCESS", "DataSyncCore")

    Exit Sub

ErrorHandler:
    Application.StatusBar = False
    Call DataHelper.ShowSyncStatus("数据同步失败: " & Err.Description)
    Call LogManager.LogSyncEvent("ERROR", "数据同步失败: " & Err.Description, "SYNC_ALL", "DataSyncCore")
    Call LogManager.EndCurrentOperationSession("SYNC_ALL", "FAILED", "DataSyncCore")
End Sub

' 从数据库刷新所有数据
Public Sub RefreshAllDataFromDB()
    On Error GoTo ErrorHandler

    ' 开始新的刷新操作会话
    Call LogManager.StartNewOperationSession("REFRESH_ALL", "DataSyncCore")

    ' 检查数据库连接
    If Not TestConnection() Then
        Call LogManager.LogSyncEvent("ERROR", "数据库连接失败，跳过刷新", "REFRESH_ALL", "DataSyncCore")
        Call LogManager.EndCurrentOperationSession("REFRESH_ALL", "FAILED", "DataSyncCore")
        Exit Sub
    End If

    ' 显示状态提示
    Application.StatusBar = "正在刷新数据..."
    DoEvents

    ' 刷新主要模块
    ' 1. 酒店信息刷新
    Call HotelInfoManager.LoadHotelInfoFromDB

    ' 2. 业务背景预测刷新
    Call BusinessForecastManager.LoadForecastDataFromDB

    ' 其他模块待添加


    ' 清除状态栏并显示完成状态
    Application.StatusBar = False
    Call DataHelper.ShowSyncStatus("所有数据刷新完成 " & Format(Now, "hh:nn:ss"))
    Call LogManager.LogSyncEvent("INFO", "所有数据刷新完成", "REFRESH_ALL", "DataSyncCore")
    Call LogManager.EndCurrentOperationSession("REFRESH_ALL", "SUCCESS", "DataSyncCore")

    Exit Sub

ErrorHandler:
    Application.StatusBar = False
    Call DataHelper.ShowSyncStatus("数据刷新失败: " & Err.Description)
    Call LogManager.LogSyncEvent("ERROR", "数据刷新失败: " & Err.Description, "REFRESH_ALL", "DataSyncCore")
    Call LogManager.EndCurrentOperationSession("REFRESH_ALL", "FAILED", "DataSyncCore")
End Sub

'
' 获取同步状态
Public Function GetSyncStatus() As String
    If TestConnection() Then
        GetSyncStatus = "数据库连接正常"
    Else
        GetSyncStatus = "数据库连接失败"
    End If
End Function





