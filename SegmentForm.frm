VERSION 5.00
Begin {C62A69F0-16DC-11CE-9E98-00AA00574A4F} SegmentForm 
   Caption         =   "生意板块选择"
   ClientHeight    =   6600
   ClientLeft      =   45
   ClientTop       =   390
   ClientWidth     =   8400
   OleObjectBlob   =   "SegmentForm.frx":0000
   StartUpPosition =   2  'CenterScreen
End
Attribute VB_Name = "SegmentForm"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False

Option Explicit

' =============================================================================
' 生意板块选择窗体 - 希尔顿酒店管理系统
' 提供三层级的生意板块数据选择界面
' =============================================================================

' 窗体初始化事件
Private Sub UserForm_Initialize()
    On Error Resume Next
    
    Call SetupForm
    Call SetupControls
    Call LogManager.LogEvent("INFO", "生意板块选择窗体初始化完成", "SegmentForm", "窗体初始化")
End Sub

' 设置窗体属性
Private Sub SetupForm()
    On Error Resume Next
    
    ' 设置窗体基本属性
    ' Me.Caption = "生意板块选择"
    ' Me.Width = 420
    ' Me.Height = 330
    Me.StartUpPosition = 2  ' 居中显示
End Sub

' 设置控件属性和布局
Private Sub SetupControls()
    On Error Resume Next
    
    '以下代码都注释掉
    ' 设置第一层业务类型单选按钮的Caption
    ' OptionRoom.Caption = "客房"
    ' OptionFB.Caption = "餐饮"
    ' OptionGME.Caption = "会议活动"
    ' OptionOther.Caption = "其他"
    
    ' 设置第二层营销漏斗阶段单选按钮的Caption
    ' OptionAwareness.Caption = "认知阶段"
    ' OptionConsideration.Caption = "考虑阶段"
    ' OptionConversion.Caption = "转化阶段"
    
    ' 设置第三层目标客群复选框的Caption
    ' CheckBoxBusinessTravelers.Caption = "商务旅客"
    ' CheckBoxFamilyTravelers.Caption = "家庭旅客"
    ' CheckBoxMillennialsGenZ.Caption = "千禧一代/Z世代"
    ' CheckBoxSeniorTravelers.Caption = "银发旅客"
    ' CheckBoxLuxuryMICE.Caption = "高端会奖"
    ' CheckBoxConference.Caption = "会议客群"
    ' CheckBoxHiltonHonorsMember.Caption = "希尔顿荣誉客会会员"
    ' CheckBoxFoodies.Caption = "美食爱好者"
    ' CheckBoxCouples.Caption = "情侣客群"
    
    ' 设置按钮Caption
    ' BtnOk.Caption = "确定"
    ' BtnCancle.Caption = "取消"
    
    ' 设置默认选择（可选）
    ' OptionRoom.Value = True  ' 默认选择客房
    ' OptionAwareness.Value = True  ' 默认选择认知阶段
End Sub

' 确定按钮点击事件
Private Sub BtnOk_Click()
    On Error GoTo ErrorHandler
    
    ' 调用SegmentFormManager处理确定事件
    Call SegmentFormManager.HandleFormOkClick
    Exit Sub
    
ErrorHandler:
    Call LogManager.LogEvent("ERROR", "确定按钮点击处理失败: " & Err.Description, "SegmentForm", "按钮事件")
    MsgBox "处理确定事件时发生错误: " & Err.Description, vbCritical, "错误"
End Sub

' 取消按钮点击事件
Private Sub BtnCancle_Click()
    On Error GoTo ErrorHandler
    
    ' 调用SegmentFormManager处理取消事件
    Call SegmentFormManager.HandleFormCancelClick
    Exit Sub
    
ErrorHandler:
    Call LogManager.LogEvent("ERROR", "取消按钮点击处理失败: " & Err.Description, "SegmentForm", "按钮事件")
    Me.Hide
End Sub

' 窗体关闭事件（用户点击X按钮）
Private Sub UserForm_QueryClose(Cancel As Integer, CloseMode As Integer)
    On Error Resume Next
    
    ' 如果是用户点击关闭按钮，执行取消逻辑
    If CloseMode = vbFormControlMenu Then
        Cancel = True  ' 取消默认关闭行为
        Call SegmentFormManager.HandleFormCancelClick
    End If
End Sub

' 重置窗体选择状态
Public Sub ResetForm()
    On Error Resume Next
    
    ' 重置第一层单选按钮
    OptionRoom.Value = False
    OptionFB.Value = False
    OptionGME.Value = False
    OptionOther.Value = False
    
    ' 重置第二层单选按钮
    OptionAwareness.Value = False
    OptionConsideration.Value = False
    OptionConversion.Value = False
    
    ' 重置第三层复选框
    CheckBoxBusinessTravelers.Value = False
    CheckBoxFamilyTravelers.Value = False
    CheckBoxMillennialsGenZ.Value = False
    CheckBoxSeniorTravelers.Value = False
    CheckBoxLuxuryMICE.Value = False
    CheckBoxConference.Value = False
    CheckBoxHiltonHonorsMember.Value = False
    CheckBoxFoodies.Value = False
    CheckBoxCouples.Value = False
    
    ' 设置默认选择
    OptionRoom.Value = True
    OptionAwareness.Value = True
    
    Call LogManager.LogEvent("INFO", "窗体选择状态已重置", "SegmentForm", "窗体重置")
End Sub
