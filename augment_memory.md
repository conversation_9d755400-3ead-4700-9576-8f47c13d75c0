

















# Code Principles
- User prefers code organization with Utils modules for common functions, centralized Token management.
- 精简代码:最大限度简化代码，减少用户心智负担，保持代码高效
- 代码复用:通用方法放入datahelper模块中，能复用的代码尽量复用
- Remove compatibility functions in favor of single optimized implementations.

# User Interface
- User prefers status information to be displayed in Excel's status bar (Application.StatusBar) rather than in worksheet cells to avoid disrupting UI layout.
- User prefers direct silent data synchronization over confirmation dialogs.
- Automatic post-login flow to welcome page with sync progress UI using Excel cells/status bar (no popups).
- Automatic navigation to main page on success or offline mode options on failure.

# Compatibility and Simplification
- 不要创建指南和总结或FixTest，除非用户明确指出！
- 简化测试: 只保留一个综合测试方法在当前模块，不要创建独立的测试文件，如果确实有必要，创建并测试完成后应删除
- 去除弹窗:去除过程弹窗，除非致命错误，其他问题在日志中输出
- Remove version update prompts in favor of direct database data retrieval for system simplicity.
- 不要使用emo或者vba不支持的icon
- 确保函数调用时byref正确
- 这是个新项目，没有数据债务，不需要考虑向前向后兼容性！
- User prefers to completely remove database logging functionality in favor of Excel-only logging for performance optimization, and prioritizes batch operations over individual database calls to reduce connection overhead.

# Code Maintenance
- When removing functions from modules, always check for dependencies in the same module and across other modules, handle all calling points before removal, and verify compilation integrity afterward.
- Maintain documentation consistency with implementation.

# Security
- Worksheet protection passwords are stored in ModGlobal module, and user prefers a unified module for managing worksheet encryption/decryption operations.