Option Explicit

' =============================================================================
' 数据辅助模块 - 希尔顿酒店管理系统
' 提供通用的数据处理和转换功能
' =============================================================================

' 获取工作表（安全版本）
Public Function GetWorksheet(sheetName As String) As Worksheet
    On Error Resume Next

    Set GetWorksheet = ThisWorkbook.Worksheets(sheetName)

    If Err.Number <> 0 Then
        Set GetWorksheet = Nothing
        Err.Clear
    End If
End Function

' 根据地址获取单元格值
Public Function GetCellValue(ws As Worksheet, cellAddress As String) As Variant
    On Error Resume Next

    If cellAddress = "" Then
        GetCellValue = ""
        Exit Function
    End If

    GetCellValue = ws.Range(cellAddress).Value

    If Err.Number <> 0 Then
        GetCellValue = ""
        Err.Clear
    End If
End Function

' SQL字符串转义
Public Function EscapeSQL(inputStr As Variant) As String
    On Error Resume Next

    If IsNull(inputStr) Or IsEmpty(inputStr) Then
        EscapeSQL = ""
        Exit Function
    End If

    Dim str As String
    str = CStr(inputStr)

    ' 转义单引号
    str = Replace(str, "'", "''")
    ' 转义反斜杠
    str = Replace(str, "\", "\\")

    EscapeSQL = str
End Function

' 将字符串转换为适合数据库decimal字段的值
Public Function ConvertToDecimalValue(inputValue As Variant) As String
    On Error Resume Next

    Dim strValue As String
    Dim numValue As Double

    ' 处理空值或Null
    If IsNull(inputValue) Or IsEmpty(inputValue) Then
        ConvertToDecimalValue = "NULL"
        Exit Function
    End If

    strValue = Trim(CStr(inputValue))

    ' 处理空字符串
    If strValue = "" Or strValue = "-" Or strValue = "0" Then
        ConvertToDecimalValue = "NULL"
        Exit Function
    End If

    ' 尝试转换为数字
    If IsNumeric(strValue) Then
        numValue = CDbl(strValue)
        ' 格式化为标准数字格式（最多4位小数）
        ConvertToDecimalValue = Format(numValue, "0.0000")
    Else
        ' 如果不是数字，静默返回NULL
        ConvertToDecimalValue = "NULL"
    End If

    ' 清除可能的错误
    If Err.Number <> 0 Then
        ConvertToDecimalValue = "NULL"
        Err.Clear
    End If
End Function

' 验证SQL语句（精简版 - 移除详细日志记录）
Public Sub ValidateSQL(sql As String, moduleName As String)
    On Error Resume Next

    ' 精简版：只做基本验证，不记录详细日志
    If Len(sql) = 0 Then Exit Sub

    ' 检查单引号配对
    Dim quoteCount As Integer
    Dim i As Integer
    For i = 1 To Len(sql)
        If Mid(sql, i, 1) = "'" Then
            quoteCount = quoteCount + 1
        End If
    Next i

    ' 如果单引号不配对，记录错误日志
    If quoteCount Mod 2 <> 0 Then
        Call LogManager.LogEvent("ERROR", "SQL语句中单引号不配对: " & moduleName, "", "SQL验证")
    End If
End Sub

' 格式化数字为显示用
Public Function FormatNumber(value As Variant) As String
    On Error Resume Next

    If IsNull(value) Or IsEmpty(value) Or value = "" Then
        FormatNumber = ""
        Exit Function
    End If

    If IsNumeric(value) Then
        FormatNumber = Format(CDbl(value), "#,##0.00")
    Else
        FormatNumber = CStr(value)
    End If

    If Err.Number <> 0 Then
        FormatNumber = CStr(value)
        Err.Clear
    End If
End Function

' 检查单元格是否为空
Public Function IsCellEmpty(ws As Worksheet, cellAddress As String) As Boolean
    Dim cellValue As Variant
    cellValue = GetCellValue(ws, cellAddress)

    IsCellEmpty = (IsNull(cellValue) Or IsEmpty(cellValue) Or Trim(CStr(cellValue)) = "")
End Function

' 安全设置单元格值
Public Sub SetCellValue(ws As Worksheet, cellAddress As String, value As Variant)
    On Error Resume Next

    If cellAddress <> "" Then
        ws.Range(cellAddress).Value = value
    End If

    If Err.Number <> 0 Then
        ' 精简版：静默处理单元格设置错误，减少日志噪音
        Err.Clear
    End If
End Sub

' 统一的状态显示函数 - 显示状态到Excel状态栏
Public Sub ShowSyncStatus(statusMessage As String)
    On Error GoTo ErrorHandler

    ' 在Excel状态栏显示状态信息
    Application.StatusBar = statusMessage
    DoEvents

    ' 精简版：不记录状态显示的调试日志，减少日志噪音

    Exit Sub

ErrorHandler:
    ' 状态显示失败不应该影响主要操作，静默处理
End Sub

' 清除状态显示 - 清除Excel状态栏
Public Sub ClearSyncStatus()
    On Error GoTo ErrorHandler

    ' 清除Excel状态栏显示
    Application.StatusBar = False
    DoEvents

    Exit Sub

ErrorHandler:
    ' 忽略清除状态时的错误
End Sub
